#!/bin/bash
echo

app_name="HCI LMA RECEIVER AGENT"
running=0

#test if start-lma.pid exists and PID  is running
if test -f "./start-lma.pid";
then
	if ps -p `cat ./start-lma.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-lma.xml --spring.profiles.active=lma 2>&1 &  echo $! > start-lma.pid
fi

echo
