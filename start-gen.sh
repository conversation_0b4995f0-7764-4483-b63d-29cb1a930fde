#!/bin/bash
echo

app_name="HCI GEN RECEIVER AGENT"
running=0

#test if start-gen.pid exists and PID  is running
if test -f "./start-gen.pid";
then
	if ps -p `cat ./start-gen.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-gen.xml --spring.profiles.active=gen 2>&1 &  echo $! > start-gen.pid
fi

echo
