package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.push.v2.SendPushPromoEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.SendPushPromoEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendPushPromoEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.MaPushEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.springframework.stereotype.Service;

@Service
public class PushPromoAgent extends BaseEventProcessingAgent<
        MaPushEvent,
        SendPushPromoEvent,
        SendPushPromoEventKafkaProducer,
        SendPushPromoEventMapper> {

    public PushPromoAgent(
            SendPushPromoEventKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            SendPushPromoEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.PUSH_SEND;
    }

    @Override
    protected Class<MaPushEvent> getInputEventClass() {
        return MaPushEvent.class;
    }
}
