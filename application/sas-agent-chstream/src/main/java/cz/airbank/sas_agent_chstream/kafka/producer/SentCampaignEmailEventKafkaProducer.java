package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.email.SentCampaignEmailEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SentCampaignEmailEventKafkaProducer extends BaseEventKafkaProducer<SentCampaignEmailEvent> {

    public SentCampaignEmailEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.CH_STREAM);
    }
}
