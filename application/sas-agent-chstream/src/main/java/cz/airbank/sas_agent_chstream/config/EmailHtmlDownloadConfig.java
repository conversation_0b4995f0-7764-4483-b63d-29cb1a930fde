package cz.airbank.sas_agent_chstream.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Email HTML Download Service
 */
@Configuration
@ConfigurationProperties(prefix = "airbank.email-html-download")
@Data
public class EmailHtmlDownloadConfig {
    
    /**
     * Whether the email HTML download service is enabled
     */
    private boolean enabled = true;
    
    /**
     * Batch size for database operations
     */
    private int batchSize = 1000;
    
    /**
     * Connection timeout in milliseconds for HTTP requests
     */
    private int connectionTimeout = 30000;
    
    /**
     * Maximum number of retries for failed operations
     */
    private int maxRetries = 3;
    
    /**
     * Delay between retries in milliseconds
     */
    private int retryDelay = 5000;
}
