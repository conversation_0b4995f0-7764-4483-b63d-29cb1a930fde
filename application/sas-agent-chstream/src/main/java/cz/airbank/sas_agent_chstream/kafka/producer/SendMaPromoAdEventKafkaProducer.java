package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.mapromoad.v5.SendMaPromoAdEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SendMaPromoAdEventKafkaProducer extends BaseEventKafkaProducer<SendMaPromoAdEvent> {

    public SendMaPromoAdEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.MA_PROMO_AD);
    }
}
