package cz.airbank.sas_agent_chstream.model.ci360;

import com.fasterxml.jackson.annotation.JsonProperty;

public record MaPushEvent(
        long tenantId,
        Attributes attributes,
        String rowKey
) implements CI360Event<EventAttributes> {
    public record Attributes(
            String externalCode,
            @JsonProperty("channel_user_id")
            String channelUserId,
            String channelType,
            String primaryDeepLink,
            @JsonProperty("creative_id")
            String creativeId,
            String secondaryVisualization,
            String vid,
            String internalTenantId,
            @JsonProperty("response_tracking_code")
            String responseTrackingCode,
            String primaryClickMeaning,
            @JsonProperty("creative_version_id")
            String creativeVersionId,
            String eventName,
            @JsonProperty("GeneralContractEntrepreneur_ApplicationStatus")
            String generalContractEntrepreneurApplicationStatus,
            String image,
            @JsonProperty("GeneralContractEntrepreneur_ApplicationId")
            String generalContractEntrepreneurApplicationId,
            @JsonProperty("event_channel")
            String eventChannel,
            @JsonProperty("task_version_id")
            String taskVersionId,
            @JsonProperty("contributing_guid_1")
            String contributingGuid1,
            String primaryVisualization,
            String priority,
            @JsonProperty("imprint_id")
            String imprintId,
            String validToRel,
            @JsonProperty("event_datetime_utc")
            String eventDatetimeUtc,
            @JsonProperty("datahub_id")
            String datahubId,
            String guid,
            String secondaryClickMeaning,
            String applicationId,
            String eventDesignedName,
            @JsonProperty("subject_id")
            String subjectId,
            String generatedTimestamp,
            String session,
            @JsonProperty("screen_info")
            String screenInfo,
            @JsonProperty("task_id")
            String taskId,
            @JsonProperty("channel_user_type")
            String channelUserType,
            @JsonProperty("parent_event")
            String parentEvent,
            String eventname,
            @JsonProperty("event_uid")
            String eventUid,
            @JsonProperty("extendedCustomEventWithRevenueFlag")
            String extendedCustomEventWithRevenueFlag,
            @JsonProperty("parent_event_uid")
            String parentEventUid,
            @JsonProperty("variant_id")
            String variantId,
            @JsonProperty("creative_content")
            String creativeContent,
            @JsonProperty("parent_eventname")
            String parentEventname,
            String event,
            String channelId,
            String timestamp,
            @JsonProperty("internal_tenant_id")
            String internalTenantId1,
            @JsonProperty("message_id")
            String messageId,
            String sessionId,
            @JsonProperty("event_category")
            String eventCategory,
            String dismissAfterGoAheadClick,
            @JsonProperty("event_designed_name") String eventDesignedName1,
            String account,
            String businessService,
            String alert,
            String subject,
            String body,
            String validFromRel,
            String urlParameterValue1,
            String primaryUrl,
            String primaryLabel,
            String secondaryUrl,
            String secondaryDeepLink,
            String secondaryLabel,
            String displayGroup,
            String validToAbs,
            String validFromAbs,
            String maPromoDetailType,
            String maVersionFrom,
            String maVersionTo
    ) implements EventAttributes {
    }
}


