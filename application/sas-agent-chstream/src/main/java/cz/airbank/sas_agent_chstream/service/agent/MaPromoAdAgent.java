package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.mapromoad.v5.SendMaPromoAdEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.SendMaPromoAdEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendMaPromoAdEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.MaBannerEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.springframework.stereotype.Service;

@Service
public class MaPromoAdAgent extends BaseEventProcessingAgent<
        MaBannerEvent,
        SendMaPromoAdEvent,
        SendMaPromoAdEventKafkaProducer,
        SendMaPromoAdEventMapper> {

    public MaPromoAdAgent(
            SendMaPromoAdEventKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            SendMaPromoAdEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.MA_PROMO_AD;
    }

    @Override
    protected Class<MaBannerEvent> getInputEventClass() {
        return MaBannerEvent.class;
    }
}