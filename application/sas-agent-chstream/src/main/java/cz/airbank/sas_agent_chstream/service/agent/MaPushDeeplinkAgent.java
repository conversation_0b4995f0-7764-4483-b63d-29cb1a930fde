package cz.airbank.sas_agent_chstream.service.agent;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas.campaign.push.deeplink.SendPushDeeplinkEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.sas_agent_chstream.kafka.producer.SendMaPushDeeplinkEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendMaPushDeeplinkEventMapper;
import cz.airbank.sas_agent_chstream.model.ci360.MaPushDeeplinkEvent;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.springframework.stereotype.Service;

@Service
public class MaPushDeeplinkAgent extends BaseEventProcessingAgent<
        MaPushDeeplinkEvent,
        SendPushDeeplinkEvent,
        SendMaPushDeeplinkEventKafkaProducer, 
        SendMaPushDeeplinkEventMapper> {

    public MaPushDeeplinkAgent(
            SendMaPushDeeplinkEventKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            SendMaPushDeeplinkEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.MA_PUSH_DEEPLINK;
    }

    @Override
    protected Class<MaPushDeeplinkEvent> getInputEventClass() {
        return MaPushDeeplinkEvent.class;
    }
}