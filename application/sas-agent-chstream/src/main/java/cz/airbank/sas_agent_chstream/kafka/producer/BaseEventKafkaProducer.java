package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import cz.airbank.spring.boot.kafka.client.AbstractKafkaProducer;
import org.springframework.kafka.core.KafkaTemplate;

public abstract class BaseEventKafkaProducer<T> extends AbstractKafkaProducer<Long, T> {

    public BaseEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig, AgentType agentType) {
        super(kafkaTemplate, applicationConfig.getKafka().getTopics().get(agentType), applicationConfig.getKafka().getTimeout());
    }
}
