package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas.campaign.ibpromoad.v3.SendIbPromoAdEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.model.ci360.BannerEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SendIbPromoAdEventMapper extends BaseMapper implements EventMapper<BannerEvent, SendIbPromoAdEvent> {

    @Override
    public SendIbPromoAdEvent mapToOutputEvent(BannerEvent inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName) {
        log.debug("Mapping data into SendPushPromoEvent");
        try {
            BannerEvent.Attributes attributes = inputEvent.attributes();
            SendIbPromoAdEvent event = new SendIbPromoAdEvent();
            event.setCuid(Long.parseLong(attributes.subjectId()));
            event.setRelatedCuid(null); //TODO
            event.setCreator("SAS360");
            event.setExternalId(leadId);
            event.setCommunicationKind(taskCache.tsk_camp_comm_type.value);
            List<String> products = new ArrayList<>();
            if (taskCache.tsk_camp_product.hasValue()) {
                String values = taskCache.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);
            event.setTaskVersionId(attributes.taskVersionId());
            event.setCampaignCode(taskCache.tsk_comm_camp_name.value);
            event.setCampaignName(campaignName);
            event.setCommunicationCode(attributes.externalCode());
            event.setCommunicationName(taskCache.taskName.value);
            event.setBusinessSummaryCauseCode(taskCache.tsk_camp_buss_cause_cd.value);
            event.setDismissAfterGoAheadClick(Boolean.parseBoolean(attributes.dismissAfterGoAheadClick()));

            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);

            event.setValidToAbs(getLocalDate(attributes.validToAbs()));
            event.setValidToRel(parseIntOrNull(attributes.validToRel()));
            event.setValidFromAbs(getLocalDateTimeInIsoOffset(attributes.validFromAbs()));
            event.setValidFromRel(parseIntOrNull(attributes.validFromRel()));

            event.setDisplayGroup(attributes.displayGroup());
            event.setPriority(Integer.parseInt(attributes.priority()));
            event.setShowGuaranteedDays(parseIntOrNull(attributes.showGuaranteedDays()));
            event.setShowMaxCountAfterGuaranteedDays(parseIntOrNull(attributes.showMaxCountAfterGuaranteedDays()));
            event.setBannerBody(getValue(attributes.bannerBody(), creativeContent.get("bannerBody")));
            event.setDetailBody(getValue(attributes.detailBody(), creativeContent.get("detailBody")));
            event.setBusinessService(attributes.businessService());

            log.debug("Successfully mapped data into SendIbPromoAdEvent");
            return event;
        } catch (Exception e) {
            log.error("Failed to map data into SendIbPromoAdEvent", e);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to SendIbPromoAdEvent", e);
        }
    }
}
