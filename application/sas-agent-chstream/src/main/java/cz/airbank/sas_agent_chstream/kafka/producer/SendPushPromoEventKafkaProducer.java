package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.push.v2.SendPushPromoEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SendPushPromoEventKafkaProducer extends BaseEventKafkaProducer<SendPushPromoEvent> {

    public SendPushPromoEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.PUSH_SEND);
    }
}
