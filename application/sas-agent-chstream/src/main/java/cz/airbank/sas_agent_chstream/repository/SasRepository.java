package cz.airbank.sas_agent_chstream.repository;

import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;

@Repository
@RequiredArgsConstructor
@Slf4j
public class SasRepository {

    private final DataSource dataSource;
    private final ApplicationConfig applicationConfig;

    public void storeErrorToDb(String leadId, String sourceChannelId, String timestamp, String reason) {
        log.debug("Storing error into database: leadId: {}, sourceChannelId: {}, reason: {}", leadId, sourceChannelId, reason);
        String sql = "INSERT INTO  " + applicationConfig.getDatabaseName() + ".CIE_INT_RESPONSE_BY_LEAD_ID (LEAD_ID, SOURCE_CHANNEL_CD, SOURCE_RESPONSE_CD, EXTERNAL_INFO_1_ID, RESPONSE_DTTM) VALUES (?, ?, ?, ?, ?) ";

        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            setStringParameter(st, 1, leadId);
            setStringParameter(st, 2, sourceChannelId);
            setStringParameter(st, 3, "ERROR");
            setStringParameter(st, 4, reason);

            Timestamp sqlTimestamp = new Timestamp(Long.parseLong(timestamp));
            st.setTimestamp(5, sqlTimestamp);

            st.execute();
        } catch (SQLException e) {
            log.error("ErrorCode:CH_02 - DB - not possible to write payload to DB", e);
            return;
        }
        log.debug("Error detail inserted into database");
    }

    public String getCampaignName(String campName) {
        log.debug("Obtaining campaign from DB for name: {}", campName);
        String sql = "select CAMP_NAME from " + applicationConfig.getDatabaseName() + ".CIE_CAMPAIGN_MESSAGE where CAMPAIGN_MESSAGE_CD = ?";

        try (
                Connection con = dataSource.getConnection();
                PreparedStatement st = con.prepareStatement(sql)
        ) {
            setStringParameter(st, 1, campName);

            ResultSet rs = st.executeQuery();

            if (rs.next()) {
                String campaignName = rs.getString("CAMP_NAME");
                log.debug("Retrieved campaign name {}", campaignName);
                return campaignName;
            }
            log.warn("No campaign found in DB for name {}", campName);
            return null;
        } catch (SQLException e) {
            log.error("ErrorCode:CH_02 - DB - not possible to read from DB", e);
            return null;
        }
    }

    protected void setStringParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null || value.toString().isEmpty()) {
            st.setNull(index, Types.VARCHAR);
        } else {
            st.setString(index, value.toString());
        }
    }

    protected void setTimestampParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.TIMESTAMP);
        } else {
            st.setTimestamp(index, (Timestamp) value);
        }
    }

    protected void setLongParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.NUMERIC);
        } else {
            st.setLong(index, (Long) value);
        }
    }
}
