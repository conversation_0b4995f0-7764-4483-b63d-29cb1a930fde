package cz.airbank.sas_agent_chstream.service;

import cz.airbank.sas_agent_chstream.model.EmailRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for handling database operations related to email records
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmailRecordDatabaseService {

    private final DataSource dataSource;
    private final JdbcTemplate jdbcTemplate;

    /**
     * Get all unprocessed email records from CIE_CONTACT_HISTORY_STREAM
     * Records older than 24 hours with ARCHIVED_FLAG = false
     */
    public List<EmailRecord> getUnprocessedEmailRecords() {
        String sql = """
            SELECT CONTACT_ID, EMAIL_IMPRINT_URL
            FROM CDM2.CIE_CONTACT_HISTORY_STREAM
            WHERE ARCHIVED_FLAG = 0
            AND CONTACT_DT < SYSDATE - 1
            AND EMAIL_IMPRINT_URL IS NOT NULL
            ORDER BY CONTACT_DT ASC
            """;

        List<EmailRecord> records = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                EmailRecord record = EmailRecord.builder()
                    .contactId(rs.getLong("CONTACT_ID"))
                    .emailImprintUrl(rs.getString("EMAIL_IMPRINT_URL"))
                    .build();
                records.add(record);
            }

        } catch (SQLException e) {
            log.error("Error retrieving unprocessed email records", e);
            throw new RuntimeException("Failed to retrieve unprocessed email records", e);
        }

        return records;
    }

    /**
     * Update ARCHIVED_FLAG to true for successfully processed records using batch operation
     * This method performs efficient batch updates for better performance with large datasets
     * 
     * @param contactIds List of contact IDs to mark as archived
     * @param batchSize Size of each batch for processing
     */
    @Transactional
    public void updateArchivedFlag(List<Long> contactIds, int batchSize) {
        if (contactIds == null || contactIds.isEmpty()) {
            log.debug("No contact IDs provided for archive flag update");
            return;
        }

        String sql = "UPDATE CDM2.CIE_CONTACT_HISTORY_STREAM SET ARCHIVED_FLAG = 1, UPDATED_DTTM = SYSTIMESTAMP WHERE CONTACT_ID = ?";

        // Process in batches to avoid memory issues with large datasets
        for (int i = 0; i < contactIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, contactIds.size());
            List<Long> batch = contactIds.subList(i, endIndex);

            List<Object[]> batchArgs = batch.stream()
                .map(id -> new Object[]{id})
                .collect(Collectors.toList());

            int[] results = jdbcTemplate.batchUpdate(sql, batchArgs);
            log.debug("Executed batch update for {} records", results.length);
        }

        log.info("Successfully updated ARCHIVED_FLAG for {} contact records", contactIds.size());
    }
}
