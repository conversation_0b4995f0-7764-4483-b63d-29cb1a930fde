package cz.airbank.sas_agent_chstream.service;

public class EmailHtmlDownloadService {
}

//Prerekvizity:
//
//Úloha navazuje na proces CH stream (stahování kontaktní historie. V rámci ní vznikla tabulka  CDM2.CIE_CONTACT_HISTORY_STREAM (viz Custom tables of Contact and Response History).
//Publikace informací o uložených záznamech v CML (spolu s CML ID záznamu) do CDM2 pomocí SAS360messageResukt topicu. Viz DWH-2929 Omnichannel - poc2 - emaily - sync do CMD2
//Postup:
//
//SAS360Proxy zajistí stažení dat a publikaci do CML.
//Aktuálně překonvertujeme HTML soubor do plaintextu a uložíme k vytvořenému kontaktu v CML pomocí jeho nové WS.
//Do budoucna (v případě potřeby stahování obrázků s významem pro právní vyhodnocení v případě sporu) se nabízí upgrade procesu, kdy se email stáhne do Cabinetu, do CML se pošle UUID souboru a rozšíří se GUI (přimárně v OBS) pro možnost stažení souboru.
//Příklad URL ze SAS360 (za méně jak 90 dní expiruje) - https://d3on7v574i947w.cloudfront.net/c/cixniceu/c9553b76-5496-48a1-a1d9-d71aefc6a4d5.html.
//
//jediná statická část URl adresy, kterou nám SAS dokáže zajistit, je "cloudfront.net"
//SAS360Proxy
//
//nová úloha proxy - archivace emailu
//implementace jobu pro stahování a archivaci emailu
//spouští se alespoň 1x denně.
//flow
//načte záznamy z CMD2 schematu, tabulky CIE_CONTACT_HISTORY_STREAM, kde ARCHIVED_FLAG==false. Bere záznamy starší jako 1 den (teoreticky by mělo stačit starší jako 15 minut kvůli procesu synchronizace)
//pro záznamy si zjistí, zda existuje záznam s CML ID - viz nalinkovaný DWH task (přepokládám, že data jsou fyzicky uložena v CDM2 schematu. Nechci šahat do DWH) pomocí externalId (identifikace SAS360). pokud existuje, pro každý z nich
//načte adresu URL adresu emailu z EMAIL_IMPRINT_URL atributu
//stáhne obsah
//obrázky se nestahují - jen odkazy na ně
// security - požadavek Sládek Ivan je, že by to mělo projít nějakou kontrolou před zpracováním.
//není to must have, tzn můžeme začít bez této kontroly
//Ivan mezitím najde způsob, jak provést kontrolu v reasonable čase
//nabízelo se využít FortiSandbox kontrolu pomocí REST. Ale prý kontrola může trvat až 10 minut na 1 email. Tzn 1 milion emailů by se jednovláknově kontroloval až 19 let
//uloží jako plaintext
//zavolá CML soap cmlContactWS.UpdateRealtimeEmailContact
//pokud v pořádku, změní ARCHIVED_FLAG na true
