package cz.airbank.sas_agent_chstream.service;

import cz.airbank.sas_agent_chstream.config.EmailHtmlDownloadConfig;
import cz.airbank.sas_agent_chstream.model.EmailRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "airbank.email-html-download.enabled", havingValue = "true", matchIfMissing = true)
public class EmailHtmlDownloadService {

    private final DataSource dataSource;
    private final EmailHtmlDownloadConfig config;

    /**
     * Scheduled job that runs daily at 1 AM to process email HTML downloads
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void processEmailHtmlDownloads() {
        log.info("Starting scheduled email HTML download processing at {}", LocalDateTime.now());

        try {
            List<EmailRecord> recordsToProcess = getUnprocessedEmailRecords();
            log.info("Found {} records to process", recordsToProcess.size());

            if (recordsToProcess.isEmpty()) {
                log.info("No records to process, exiting");
                return;
            }

            processRecords(recordsToProcess);

        } catch (Exception e) {
            log.error("Error during scheduled email HTML download processing", e);
        }

        log.info("Completed scheduled email HTML download processing at {}", LocalDateTime.now());
    }

    /**
     * Get all unprocessed email records from CIE_CONTACT_HISTORY_STREAM
     * Records older than 24 hours with ARCHIVED_FLAG = false
     */
    private List<EmailRecord> getUnprocessedEmailRecords() {
        String sql = """
            SELECT CONTACT_ID, EMAIL_IMPRINT_URL, CONTACT_DT, CONTACT_DTTM, IDENTITY_ID, SUBJECT_ID
            FROM CDM2.CIE_CONTACT_HISTORY_STREAM
            WHERE ARCHIVED_FLAG = 0
            AND CONTACT_DT < SYSDATE - 1
            AND EMAIL_IMPRINT_URL IS NOT NULL
            ORDER BY CONTACT_DT ASC
            """;

        List<EmailRecord> records = new ArrayList<>();

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                EmailRecord record = EmailRecord.builder()
                    .contactId(rs.getLong("CONTACT_ID"))
                    .emailImprintUrl(rs.getString("EMAIL_IMPRINT_URL"))
                    .contactDate(rs.getTimestamp("CONTACT_DT"))
                    .contactDateTime(rs.getTimestamp("CONTACT_DTTM"))
                    .identityId(rs.getString("IDENTITY_ID"))
                    .subjectId(rs.getString("SUBJECT_ID"))
                    .build();
                records.add(record);
            }

        } catch (SQLException e) {
            log.error("Error retrieving unprocessed email records", e);
            throw new RuntimeException("Failed to retrieve unprocessed email records", e);
        }

        return records;
    }

    /**
     * Process the list of email records with retry logic and error handling
     */
    private void processRecords(List<EmailRecord> records) {
        List<Long> successfullyProcessedIds = new ArrayList<>();
        int totalRecords = records.size();
        int processedCount = 0;
        int errorCount = 0;

        log.info("Starting to process {} email records", totalRecords);

        for (EmailRecord record : records) {
            boolean processed = false;
            int retryCount = 0;

            while (!processed && retryCount <= config.getMaxRetries()) {
                try {
                    log.debug("Processing record with CONTACT_ID: {} (attempt {}/{})",
                             record.getContactId(), retryCount + 1, config.getMaxRetries() + 1);

                    // Download and convert HTML to plain text
                    String plainText = downloadAndConvertHtml(record.getEmailImprintUrl());

                    if (plainText == null || plainText.trim().isEmpty()) {
                        log.warn("Empty or null plain text extracted for CONTACT_ID: {}", record.getContactId());
                        break; // Don't retry for empty content
                    }

                    // Update CML (placeholder method)
                    updateCML(record, plainText);

                    // Mark as successfully processed
                    successfullyProcessedIds.add(record.getContactId());
                    processed = true;
                    processedCount++;

                    log.debug("Successfully processed record with CONTACT_ID: {}", record.getContactId());

                } catch (Exception e) {
                    retryCount++;
                    if (retryCount <= config.getMaxRetries()) {
                        log.warn("Error processing record with CONTACT_ID: {} (attempt {}/{}). Retrying in {} ms",
                                record.getContactId(), retryCount, config.getMaxRetries() + 1, config.getRetryDelay(), e);
                        try {
                            Thread.sleep(config.getRetryDelay());
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            log.error("Thread interrupted during retry delay", ie);
                            break;
                        }
                    } else {
                        log.error("Failed to process record with CONTACT_ID: {} after {} attempts",
                                 record.getContactId(), config.getMaxRetries() + 1, e);
                        errorCount++;
                    }
                }
            }
        }

        // Batch update ARCHIVED_FLAG for successfully processed records
        if (!successfullyProcessedIds.isEmpty()) {
            try {
                updateArchivedFlag(successfullyProcessedIds);
                log.info("Successfully processed and archived {} out of {} records. Errors: {}",
                        processedCount, totalRecords, errorCount);
            } catch (Exception e) {
                log.error("Failed to update ARCHIVED_FLAG for processed records", e);
            }
        } else {
            log.warn("No records were successfully processed out of {} total records", totalRecords);
        }
    }

    /**
     * Update CML with the processed email content
     * This method will be implemented later once WSDL definition is available
     *
     * @param record The email record containing contact information
     * @param plainText The converted plain text content from the HTML email
     */
    private void updateCML(EmailRecord record, String plainText) {
        // TODO: Implement CML SOAP call once WSDL definition is available
        // This should call cmlContactWS.UpdateRealtimeEmailContact with the following parameters:
        // - Contact ID from record.getContactId()
        // - Identity ID from record.getIdentityId()
        // - Subject ID from record.getSubjectId()
        // - Plain text content from plainText parameter
        // - Contact date/time information from record

        log.debug("CML update placeholder called for CONTACT_ID: {} with text length: {}",
                 record.getContactId(), plainText != null ? plainText.length() : 0);

        // For now, just log the information that would be sent to CML
        log.info("Would update CML for contact: ID={}, IdentityID={}, SubjectID={}, TextLength={}",
                record.getContactId(),
                record.getIdentityId(),
                record.getSubjectId(),
                plainText != null ? plainText.length() : 0);
    }

    /**
     * Download HTML from URL and convert to plain text
     * Images are not downloaded, only links to them are preserved
     */
    private String downloadAndConvertHtml(String url) {
        if (url == null || url.trim().isEmpty()) {
            log.warn("Empty or null URL provided for HTML download");
            return null;
        }

        try {
            log.debug("Downloading HTML from URL: {}", url);

            // Download HTML with JSoup - this doesn't download images, just the HTML content
            Document document = Jsoup.connect(url)
                    .timeout(config.getConnectionTimeout())
                    .userAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .followRedirects(true)
                    .ignoreContentType(false)
                    .get();

            // Convert to plain text while preserving some structure
            String plainText = document.text();

            log.debug("Successfully converted HTML to plain text. Original URL: {}, Text length: {}",
                     url, plainText.length());

            return plainText;

        } catch (SocketTimeoutException e) {
            log.error("Timeout while downloading HTML from URL: {}", url, e);
            throw new RuntimeException("Timeout downloading HTML from: " + url, e);
        } catch (IOException e) {
            log.error("IO error while downloading HTML from URL: {}", url, e);
            throw new RuntimeException("Failed to download HTML from: " + url, e);
        } catch (Exception e) {
            log.error("Unexpected error while downloading HTML from URL: {}", url, e);
            throw new RuntimeException("Unexpected error downloading HTML from: " + url, e);
        }
    }

    /**
     * Update ARCHIVED_FLAG to true for successfully processed records using batch operation
     * This method performs efficient batch updates for better performance with large datasets
     */
    @Transactional
    private void updateArchivedFlag(List<Long> contactIds) {
        if (contactIds == null || contactIds.isEmpty()) {
            log.debug("No contact IDs provided for archive flag update");
            return;
        }

        String sql = "UPDATE CDM2.CIE_CONTACT_HISTORY_STREAM SET ARCHIVED_FLAG = 1, UPDATED_DTTM = SYSTIMESTAMP WHERE CONTACT_ID = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            conn.setAutoCommit(false); // Start transaction

            int batchSize = 0;
            final int MAX_BATCH_SIZE = config.getBatchSize(); // Process in configurable batches

            for (Long contactId : contactIds) {
                stmt.setLong(1, contactId);
                stmt.addBatch();
                batchSize++;

                // Execute batch when reaching max size
                if (batchSize >= MAX_BATCH_SIZE) {
                    int[] results = stmt.executeBatch();
                    conn.commit();
                    log.debug("Executed batch update for {} records", results.length);
                    batchSize = 0;
                }
            }

            // Execute remaining batch
            if (batchSize > 0) {
                int[] results = stmt.executeBatch();
                conn.commit();
                log.debug("Executed final batch update for {} records", results.length);
            }

            log.info("Successfully updated ARCHIVED_FLAG for {} contact records", contactIds.size());

        } catch (SQLException e) {
            log.error("Error updating ARCHIVED_FLAG for contact IDs", e);
            throw new RuntimeException("Failed to update ARCHIVED_FLAG for processed records", e);
        }
    }
}
