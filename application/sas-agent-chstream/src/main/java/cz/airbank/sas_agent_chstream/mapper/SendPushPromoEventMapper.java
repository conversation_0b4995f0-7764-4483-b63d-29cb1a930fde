package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas.campaign.push.v2.SendPushPromoEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.model.ci360.MaPushEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SendPushPromoEventMapper extends BaseMapper implements EventMapper<MaPushEvent, SendPushPromoEvent>{

    @Override
    public SendPushPromoEvent mapToOutputEvent(MaPushEvent inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName) {
        log.debug("Mapping data into SendPushPromoEvent");
        try {
            MaPushEvent.Attributes attributes = inputEvent.attributes();
            SendPushPromoEvent event = new SendPushPromoEvent();
            event.setCuid(Long.parseLong(attributes.subjectId()));
            event.setRelatedCuid(null); //TODO
            event.setGeneralContractNumber(null); //TODO
            event.setCreator("SAS360");
            event.setExternalId(leadId);
            event.setCommunicationKind(taskCache.tsk_camp_comm_type.value);
            List<String> products = new ArrayList<>();
            if (taskCache.tsk_camp_product.hasValue()) {
                String values = taskCache.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);
            event.setTaskVersionId(attributes.taskVersionId());
            event.setCampaignCode(taskCache.tsk_comm_camp_name.value);
            event.setCampaignName(campaignName);
            event.setCommunicationCode(attributes.externalCode());
            event.setCommunicationName(taskCache.taskName.value);
            event.setBusinessSummaryCauseCode(taskCache.tsk_camp_buss_cause_cd.value);
            event.setBusinessService(attributes.businessService());
            event.setDismissAfterGoAheadClick(Boolean.parseBoolean(attributes.dismissAfterGoAheadClick()));

            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);

            event.setAlert(getValue(attributes.alert(), creativeContent.get("alert")));
            event.setSubject(getValue(attributes.subject(), creativeContent.get("subject")));
            event.setBody(getValue(attributes.body(), creativeContent.get("body")));
            event.setImage(getValue(attributes.image(), creativeContent.get("image")));
            event.setValidToAbs(getLocalDate(attributes.validToAbs()));
            try {
                event.setValidToRel(Double.parseDouble(attributes.validToRel()));
            } catch (Exception e) {
                event.setValidToRel(null);
            }
            event.setValidFromAbs(getLocalDateTimeInIsoOffset(attributes.validFromAbs()));
            try {
                event.setValidFromRel(Double.parseDouble(attributes.validFromRel()));
            } catch (Exception e) {
                event.setValidFromRel(null);
            }
            event.setUrlParameterValue1(getValue(attributes.urlParameterValue1(), creativeContent.get("urlParameterValue1")));
            event.setPrimaryUrl(getValue(attributes.primaryUrl(), creativeContent.get("primaryUrl")));
            event.setPrimaryClickMeaning(getValue(attributes.primaryClickMeaning(), creativeContent.get("primaryClickMeaning")));
            event.setPrimaryDeepLink(getValue(attributes.primaryDeepLink(), creativeContent.get("primaryDeepLink")));
            event.setPrimaryLabel(getValue(attributes.primaryLabel(), creativeContent.get("primaryLabel")));
            event.setPrimaryVisualization(getValue(attributes.primaryVisualization(), creativeContent.get("primaryVisualization")));
            event.setSecondaryUrl(getValue(attributes.secondaryUrl(), creativeContent.get("secondaryUrl")));
            event.setSecondaryDeepLink(getValue(attributes.secondaryDeepLink(), creativeContent.get("secondaryDeepLink")));
            event.setSecondaryClickMeaning(getValue(attributes.secondaryClickMeaning(), creativeContent.get("secondaryClickMeaning")));
            event.setSecondaryLabel(getValue(attributes.secondaryLabel(), creativeContent.get("secondaryLabel")));
            event.setSecondaryVisualization(getValue(attributes.secondaryVisualization(), creativeContent.get("secondaryVisualization")));
            event.setDisplayGroup(attributes.displayGroup());
            event.setPriority(Long.parseLong(attributes.priority()));
            event.setMaPromoDetailType(getValue(attributes.maPromoDetailType(), creativeContent.get("maPromoDetailType")));
            event.setMaVersionFrom(getValue(attributes.maVersionFrom(), creativeContent.get("maVersionFrom")));
            event.setMaVersionTo(getValue(attributes.maVersionTo(), creativeContent.get("maVersionTo")));

            log.debug("Successfully mapped data into SendPushPromoEvent");
            return event;
        } catch (Exception e) {
            log.error("Failed to map data into SendPushPromoEvent", e);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to SendPushPromoEvent", e);
        }
    }
}
