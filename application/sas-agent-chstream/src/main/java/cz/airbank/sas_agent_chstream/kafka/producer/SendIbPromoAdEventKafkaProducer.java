package cz.airbank.sas_agent_chstream.kafka.producer;

import cz.airbank.sas.campaign.ibpromoad.v3.SendIbPromoAdEvent;
import cz.airbank.sas_agent_chstream.config.ApplicationConfig;
import cz.airbank.sas_agent_chstream.enumeration.AgentType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class SendIbPromoAdEventKafkaProducer extends BaseEventKafkaProducer<SendIbPromoAdEvent> {

    public SendIbPromoAdEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate, ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.IB_PROMO_AD);
    }
}
