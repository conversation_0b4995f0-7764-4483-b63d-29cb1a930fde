package cz.airbank.sas_agent_chstream.model.ci360;

import com.fasterxml.jackson.annotation.JsonProperty;

public record CampaignCallEvent(
        long tenantId,
        Attributes attributes,
        String rowKey
) implements CI360Event<EventAttributes> {
    public record Attributes(
            String externalCode,
            @JsonProperty("channel_user_id")
            String channelUserId,
            String channelType,
            @JsonProperty("activity_node_id")
            String activityNodeId,
            String primaryDeepLink,
            @JsonProperty("creative_id")
            String creativeId,
            String secondaryVisualization,
            String vid,
            String internalTenantId,
            @JsonProperty("response_tracking_code")
            String responseTrackingCode,
            String primaryClickMeaning,
            @JsonProperty("activity_id")
            String activityId,
            @JsonProperty("creative_version_id")
            String creativeVersionId,
            String eventName,
            String image,
            @JsonProperty("event_channel")
            String eventChannel,
            @JsonProperty("task_version_id")
            String taskVersionId,
            @JsonProperty("abtest_path_id")
            String abtestPathId,
            String displayGroup,
            String primaryVisualization,
            String priority,
            @JsonProperty("activity_task_type")
            String activityTaskType,
            String validToRel,
            String validFromRel,
            @JsonProperty("event_datetime_utc")
            String eventDatetimeUtc,
            @JsonProperty("datahub_id")
            String datahubId,
            String guid,
            String secondaryClickMeaning,
            String eventDesignedName,
            @JsonProperty("subject_id")
            String subjectId,
            @JsonProperty("activity_timebox")
            String activityTimebox,
            String generatedTimestamp,
            @JsonProperty("screen_info")
            String screenInfo,
            @JsonProperty("task_id")
            String taskId,
            @JsonProperty("activity_ia_tag_value")
            String activityIaTagValue,
            String eventname,
            @JsonProperty("event_uid")
            String eventUid,
            String extendedCustomEventWithRevenueFlag,
            @JsonProperty("creative_content")
            String creativeContent,
            String event,
            String channelId,
            String timestamp,
            @JsonProperty("internal_tenant_id")
            String internalTenantId1,
            @JsonProperty("event_category")
            String eventCategory,
            String dismissAfterGoAheadClick,
            @JsonProperty("customer_id")
            String customerId,
            @JsonProperty("event_designed_name")
            String eventDesignedName1,
            String account,
            String useDetail,
            String place,
            String message,
            String subject,
            String businessKey,
            String validToAbs,
            String validFromAbs,
            String campaignCommunicationType
    ) implements EventAttributes {
    }
}


