package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas.campaign.push.deeplink.SendPushDeeplinkEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.model.ci360.MaPushDeeplinkEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SendMaPushDeeplinkEventMapper extends BaseMapper implements EventMapper<MaPushDeeplinkEvent, SendPushDeeplinkEvent> {

    @Override
    public SendPushDeeplinkEvent mapToOutputEvent(MaPushDeeplinkEvent inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName) {
        log.debug("Mapping data into SendPushPromoEvent");
        try {
            MaPushDeeplinkEvent.Attributes attributes = inputEvent.attributes();
            SendPushDeeplinkEvent event = new SendPushDeeplinkEvent();
            event.setCuid(Long.parseLong(attributes.subjectId()));
            event.setCreator("SAS360");
            event.setExternalId(leadId);
            event.setCommunicationKind(taskCache.tsk_camp_comm_type.value);
            List<String> products = new ArrayList<>();
            if (taskCache.tsk_camp_product.hasValue()) {
                String values = taskCache.tsk_camp_product.value;
                List<String> valueList = Arrays.asList(values.split(","));
                products.addAll(valueList);
            }
            event.setProducts(products);
            event.setTaskVersionId(attributes.taskVersionId());
            event.setCampaignCode(taskCache.tsk_comm_camp_name.value);
            event.setCampaignName(campaignName);
            event.setCommunicationCode(attributes.externalCode());
            event.setCommunicationName(taskCache.taskName.value);
            event.setBusinessSummaryCauseCode(taskCache.tsk_camp_buss_cause_cd.value);
            event.setBusinessService(attributes.businessService());
            LocalDateTime now = LocalDateTime.now();
            String formattedNow = getLocalDateTimeInIsoOffset(now);
            event.setCreated(formattedNow);

            event.setAlert(getValue(attributes.alert(), creativeContent.get("alert")));
            event.setTitle(getValue(attributes.title(), creativeContent.get("title")));
            event.setUrl(getValue(attributes.url(), creativeContent.get("url")));

            event.setDisplayGroup(attributes.displayGroup());
            event.setPriority(Integer.parseInt(attributes.priority()));

            log.debug("Successfully mapped data into SendPushPromoEvent");
            return event;
        } catch (Exception e) {
            log.error("Failed to map data into SendPushPromoEvent", e);
            throw new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to SendPushPromoEvent", e);
        }
    }
}
