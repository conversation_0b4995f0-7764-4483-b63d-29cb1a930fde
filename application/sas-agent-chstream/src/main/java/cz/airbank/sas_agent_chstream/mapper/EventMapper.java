package cz.airbank.sas_agent_chstream.mapper;

import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;

import java.util.Map;

/**
 * Generic interface for event mappers.
 * 
 * @param <I> Input event type
 * @param <O> Output event type
 */
public interface EventMapper<I, O> {
    
    /**
     * Maps input event attributes to output event.
     * 
     * @param inputEvent The input event
     * @param creativeContent The parsed creative content
     * @param taskCache The task cache entry
     * @param leadId The generated lead ID
     * @param campaignName The campaign name
     * @return The mapped output event
     */
    O mapToOutputEvent(I inputEvent, Map<String, String> creativeContent, TaskCacheEntry taskCache, String leadId, String campaignName);
}
