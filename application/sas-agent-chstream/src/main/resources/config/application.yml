env:
  name: ${env_name:de16}

server:
  port: 8080

kafka:
  ab:
    username: SAS_KAFKA_USER
    password: SAS_KAFKA_USER
    groupId: sas-agent-kafka
    apicurioRegistryUrl: "http://kafka-ui.np.ab:8080/apis/registry/v2/"
    bootstrap-servers: "kafka-01.np.ab:9092,kafka-02.np.ab:9092,kafka-03.np.ab:9092"
    topics:
      sentCampaignEmail: ${env.name}.cz.airbank.sas.campaign.email.sent.v1
      sendPromoPushEvent: ${env.name}.cz.airbank.sas.campaign.push.send.v2
      sendIbPromoAd: ${env.name}.cz.airbank.sas.campaign.ib.promoad.send.v3
      sendMaPromoAd: ${env.name}.cz.airbank.sas.campaign.ma.promoad.send.v5
      storeCampaignPlannedCall: ${env.name}.cz.airbank.sas.campaign.planned.call.v1
      maPushDeeplink: ${env.name}.cz.airbank.sas.campaign.push.deeplink.v1

airbank:
  ci360:
    gateway: extapigwservice-eu-prod.ci360.sas.com
    tenant-id: f0cb22506600016b1805ee8b
    client-secret: MjUxNDExMzltM2MzZDgyZzNjbGJqaTNhMzJqZWkxM2lqY2Mw
    url: https://extapigwservice-eu-prod.ci360.sas.com/
    token: #TODO
    application-id: SasAgentKafka
    proxy:
      host: proxy.np.ab
      port: 3128
      username: sascloud_user
      password: #TODO
      realm: proxy
      non-proxy-hosts: app-sas-test.np.ab
  events:
    ch-stream:
      prefix: c_
      ignored-events: c_activitystart,c_abtestpathassignment
      direct-event: c_direct
    push-send:
      prefix: MA_PUSH
    ib-promo-ad:
      prefix: BANNER
    ma-promo-ad:
      prefix: MA_BANNER
    campaign-planned-call:
      prefix: CALL
    ma-push-deeplink:
      prefix: MA_PUSH_DEEPLINK
  http-client:
    connect-timeout: 10
    timeout: 30
  kafka:
    timeout: 60000
    topics:
      ch-stream: ${kafka.ab.topics.sentCampaignEmail}
      push-send: ${kafka.ab.topics.sendPromoPushEvent}
      ib-promo-ad: ${kafka.ab.topics.sendIbPromoAd}
      ma-promo-ad: ${kafka.ab.topics.sendMaPromoAd}
      campaign-planned-call: ${kafka.ab.topics.storeCampaignPlannedCall}
      ma-push-deeplink: ${kafka.ab.topics.maPushDeeplink}
  database-name: APP_CAMPAIGN_CDM2
  email-download:
    enabled: true
    batch-size: 1000
    connection-timeout: 30000
    max-retries: 3
    retry-delay: 5000

spring:
  datasource:
    url: "*************************.${env.name}.NP.AB:1621/MN${env.name}DW.NP.AB"
    username: APP_CAMPAIGN_CDM2
    password: APP_CAMPAIGN_CDM2
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 1
  task:
    execution:
      pool:
        max-size: 5000
management:
  endpoints:
    web:
      exposure:
        include: [ "prometheus", "health", "info" ]