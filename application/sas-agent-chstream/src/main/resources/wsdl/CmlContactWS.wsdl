<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:common="http://airbank.cz/common/ws/fault"
                  xmlns:tns="http://airbank.cz/cml/ws/contact"
                  targetNamespace="http://airbank.cz/cml/ws/contact">
    <wsdl:types>
        <xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified" targetNamespace="http://airbank.cz/cml/ws/contact">
            <xsd:include schemaLocation="CmlContactWS.xsd"/>
        </xsd:schema>
        <xsd:schema targetNamespace="http://airbank.cz/common/ws/fault">
            <xsd:include schemaLocation="../xsd/commonSoapFault.xsd"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="UpdateRealtimeEmailContactRequest">
        <wsdl:part element="tns:UpdateRealtimeEmailContactRequest" name="UpdateRealtimeEmailContactRequest"/>
    </wsdl:message>
    <wsdl:message name="UpdateRealtimeEmailContactResponse">
        <wsdl:part element="tns:UpdateRealtimeEmailContactResponse" name="UpdateRealtimeEmailContactResponse"/>
    </wsdl:message>
    <wsdl:message name="UpdateRealtimeEmailContactFaultMessage">
        <wsdl:part element="common:CoreFaultElement" name="UpdateRealtimeEmailContactFault"/>
    </wsdl:message>

    <wsdl:portType name="CmlContactWS">
        <wsdl:operation name="UpdateRealtimeEmailContact">
            <wsdl:documentation>
                https://wiki.airbank.cz/x/FJDaGQ
            </wsdl:documentation>
            <wsdl:input message="tns:UpdateRealtimeEmailContactRequest" name="UpdateRealtimeEmailContactRequest"/>
            <wsdl:output message="tns:UpdateRealtimeEmailContactResponse" name="UpdateRealtimeEmailContactResponse"/>
            <wsdl:fault message="tns:UpdateRealtimeEmailContactFaultMessage" name="UpdateRealtimeEmailContactFault"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CmlContactWSSoap11" type="tns:CmlContactWS">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="UpdateRealtimeEmailContact">
            <wsdl:input>
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal"/>
            </wsdl:output>
            <wsdl:fault name="UpdateRealtimeEmailContactFault">
                <soap:fault name="UpdateRealtimeEmailContactFault" use="literal"/>
            </wsdl:fault>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CmlContactWSService">
        <wsdl:port binding="tns:CmlContactWSSoap11" name="CmlContactWSSoap11">
            <soap:address location="http://localhost:8087/ws/CmlContactWS"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>