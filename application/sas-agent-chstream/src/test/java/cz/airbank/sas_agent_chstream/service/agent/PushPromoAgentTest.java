package cz.airbank.sas_agent_chstream.service.agent;

import cz.airbank.sas.campaign.push.v2.SendPushPromoEvent;
import cz.airbank.sas_agent_chstream.cache.TaskCache;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.config.ObjectMapperConfiguration;
import cz.airbank.sas_agent_chstream.exception.CHStreamAgentException;
import cz.airbank.sas_agent_chstream.kafka.producer.SendPushPromoEventKafkaProducer;
import cz.airbank.sas_agent_chstream.mapper.SendPushPromoEventMapper;
import cz.airbank.sas_agent_chstream.repository.SasRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UncheckedIOException;
import java.util.List;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = { ObjectMapperConfiguration.class, PushPromoAgent.class})
class PushPromoAgentTest {

    @MockBean
    SendPushPromoEventKafkaProducer producer;

    @MockBean
    TaskCache taskCache;

    @MockBean
    SasRepository sasRepository;

    @MockBean
    SendPushPromoEventMapper mapper;

    @Autowired
    private PushPromoAgent pushPromoAgent;

    @Test
    void processMessage_success() {
        String event = readFileToString("classpath:payload_ma_push.json");
        String campaignName = "campaignName";
        TaskCacheEntry taskCacheEntry = new TaskCacheEntry();
        SendPushPromoEvent validEvent = validEvent();

        when(taskCache.getTask(anyString(), anyString())).thenReturn(taskCacheEntry);
        when(sasRepository.getCampaignName(any())).thenReturn(campaignName);
        when(mapper.mapToOutputEvent(any(), any(), eq(taskCacheEntry), any(), eq(campaignName)))
                .thenReturn(validEvent);

        pushPromoAgent.processMessage(event);

        verify(producer).publish(validEvent.getCuid(), validEvent);
    }

    @Test
    void processMessage_noTaskCache() {
        String event = readFileToString("classpath:payload_ma_push.json");

        when(taskCache.getTask(anyString(), anyString())).thenReturn(null);

        pushPromoAgent.processMessage(event);

        verifyNoInteractions(producer, sasRepository, mapper);
    }

    @Test
    void processMessage_mappingException() {
        String event = readFileToString("classpath:payload_ma_push.json");
        String campaignName = "campaignName";
        TaskCacheEntry taskCacheEntry = new TaskCacheEntry();

        when(taskCache.getTask(anyString(), anyString())).thenReturn(taskCacheEntry);
        when(sasRepository.getCampaignName(any())).thenReturn(campaignName);
        when(mapper.mapToOutputEvent(any(), any(), eq(taskCacheEntry), any(), eq(campaignName)))
                .thenThrow(new CHStreamAgentException("Invalid field type"));

        pushPromoAgent.processMessage(event);

        verifyNoInteractions(producer);
        verify(sasRepository).storeErrorToDb(any(), any(), any(), eq("Invalid field type"));
    }

    @Test
    void processMessage_validationException() {
        String event = readFileToString("classpath:payload_ma_push.json");
        String campaignName = "campaignName";
        TaskCacheEntry taskCacheEntry = new TaskCacheEntry();
        SendPushPromoEvent invalidEvent = new SendPushPromoEvent();

        when(taskCache.getTask(anyString(), anyString())).thenReturn(taskCacheEntry);
        when(sasRepository.getCampaignName(any())).thenReturn(campaignName);
        when(mapper.mapToOutputEvent(any(), any(), eq(taskCacheEntry), any(), eq(campaignName)))
                .thenReturn(invalidEvent);

        pushPromoAgent.processMessage(event);

        verifyNoInteractions(producer);
        verify(sasRepository).storeErrorToDb(any(), any(), any(), contains("ErrorCode:CH_02 - Validation error - missing mandatory attributes in payload:"));
    }

    @Test
    void processMessage_eventMappingException() {
        String event = readFileToString("classpath:payload_ma_push.json");
        String campaignName = "campaignName";
        TaskCacheEntry taskCacheEntry = new TaskCacheEntry();

        when(taskCache.getTask(anyString(), anyString())).thenReturn(taskCacheEntry);
        when(sasRepository.getCampaignName(any())).thenReturn(campaignName);
        when(mapper.mapToOutputEvent(any(), any(), eq(taskCacheEntry), any(), eq(campaignName)))
                .thenThrow(new CHStreamAgentException("ErrorCode:CH_02 - Validation error - failed mapping to SendPushPromoEvent"));

        pushPromoAgent.processMessage(event);

        verifyNoInteractions(producer);
        verify(sasRepository).storeErrorToDb(any(), any(), any(), contains("ErrorCode:CH_02 - Validation error - failed mapping to SendPushPromoEvent"));
    }

    @Test
    void processMessage_parseJsonException() {
        String event = "somebullshit";

        pushPromoAgent.processMessage(event);

        verifyNoInteractions(producer, sasRepository, taskCache, mapper);
    }

    public static String readFileToString(String path) {
        ResourceLoader resourceLoader = new DefaultResourceLoader();
        Resource resource = resourceLoader.getResource(path);
        try (Reader reader = new InputStreamReader(resource.getInputStream(), UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }


    private SendPushPromoEvent validEvent() {
        SendPushPromoEvent event = new SendPushPromoEvent();

        event.setCuid(651576160);
        event.setCreator("creator");
        event.setExternalId("externalId");
        event.setCommunicationKind("communicationKind");
        event.setProducts(List.of("Products"));
        event.setTaskVersionId("taskVersionId");
        event.setCampaignCode("campaignCode");
        event.setCampaignName("campaignName");
        event.setCommunicationCode("communicationCode");
        event.setCommunicationName("communicationName");
        event.setCreated("created");
        event.setBody("body");
        event.setSubject("subject");
        event.setAlert("alert");
        return event;
    }
}