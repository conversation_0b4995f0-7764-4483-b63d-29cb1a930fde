package cz.airbank.sas_agent_chstream.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.airbank.sas_agent_chstream.cache.TaskCacheEntry;
import cz.airbank.sas_agent_chstream.config.ObjectMapperConfiguration;
import cz.airbank.sas_agent_chstream.model.ci360.BannerEvent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UncheckedIOException;
import java.util.Map;

import static cz.airbank.sas_agent_chstream.util.CreativeContentUtil.parseCreativeContent;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.assertj.core.api.Assertions.assertThatNoException;

@SpringBootTest
@ContextConfiguration(classes = { ObjectMapperConfiguration.class, SendIbPromoAdEventMapper.class})
class SendIbPromoAdEventMapperTest {

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    SendIbPromoAdEventMapper sendIbPromoAdEventMapper;

    @Test
    void mapperTest() throws JsonProcessingException {
        String event = readFileToString("classpath:payload_ib_promo.json");
        TaskCacheEntry taskCacheEntry = new TaskCacheEntry();
        BannerEvent bannerEvent = objectMapper.readValue(event, BannerEvent.class);
        Map<String, String> creativeContent = parseCreativeContent(bannerEvent.attributes().creativeContent());

        assertThatNoException().isThrownBy(() ->
            sendIbPromoAdEventMapper.mapToOutputEvent(bannerEvent, creativeContent, taskCacheEntry, "leadId", "campaignName")
        );
    }

    public static String readFileToString(String path) {
        ResourceLoader resourceLoader = new DefaultResourceLoader();
        Resource resource = resourceLoader.getResource(path);
        try (Reader reader = new InputStreamReader(resource.getInputStream(), UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        } catch (IOException e) {
            throw new UncheckedIOException(e);
        }
    }
}