# SAS Agent CHStream
This application is designed to listen to CI360 stream, process incoming messages, convert them according to `CHStreamAgentService` mapping, and store into database. If the message satisfies given conditions, it is sent to Kafka topic. As the incoming message does not contain all relevant information, the application obtains remaining information from in memory cache, database, or CI360 REST API (in that order). It leverages Spring Boot for its operational framework, Kafka for messaging, and a configurable application.yml for flexibility.

## Features
- Listen to CI360 message stream.
- Convert CI360 messages to database domain.
- Send messages to Kafka topic.
- Retrieve additional information from CI360 external system via REST API.
- Store additional information to in-memory cache and database.

## Configuration

### Application Configuration
The application's behavior is driven by settings defined in application.yml. Below are the key configurations:

#### Kafka Settings
- `airbank.kafka.topic`: Kafka topics the application will send messages to.
#### Database Settings
- `airbank.database-name`: Name of database where the converted messages will be stored.
- `spring.datasource.*`: Database configuration containing jdbc url, credentials, pool size, etc.
#### CI360 API Settings
- `airbank.ci360.gateway`: The Gateway of CI360 stream.
- `airbank.ci360.client-secret`: The client secret used for connection to CI360 stream.
- `airbank.ci360.token`: The token used for connection to CI360 stream.
- `airbank.ci360.url`: The URL of the CI360 API endpoint.
- `airbank.ci360.api-token`: Authentication token for the CI360 API.
- `airbank.ci360.application-id`: Application ID used for the CI360 API.
#### Event Settings
- `airbank.event.prefix`: Prefix of event names that will be processed.
- `airbank.event.ignored-events`: List of event names that suffices the prefix condition, but should be skipped.
- `airbank.event.direct-event`: Name of event that is of type Direct event

## Getting Started

### Prerequisites
- Java JDK 21 or newer.
- Access to a Kafka broker and schema registry.
- Connectivity to the CI360 API.

### Running the Application
- Clone the repository and navigate to the application directory.
- Ensure application.yml is configured according to your environment.
#### Build the application with Maven:
`mvn clean package`
#### Run the application:
`java -jar target/your-application.jar`

### Monitoring
The application exposes Actuator endpoints for monitoring and management, configurable under management.endpoints.web.exposure.include.

## Adding New Channels

The application uses a generic agent architecture that significantly reduces code duplication. When adding a new channel, follow these steps:

### 1. Add New Agent Type Enum

Add a new entry to `AgentType` enum in `cz.airbank.sas_agent_chstream.enumeration.AgentType`:

```java
public enum AgentType {
    MA_PUSH_DEEPLINK,
    MA_PROMO_AD,
    IB_PROMO_AD,
    CAMPAIGN_PLANNED_CALL,
    PUSH_SEND,
    YOUR_NEW_CHANNEL  // Add your new channel here
}
```

### 2. Update Configuration Mappings

Add both the Kafka topic mapping and event prefix mapping in `application.yml`:

```yaml
airbank:
  kafka:
    topics:
      MA_PUSH_DEEPLINK: "ma-promo-push-deeplink-topic"
      MA_PROMO_AD: "ma-promo-ad-topic"
      IB_PROMO_AD: "ib-promo-ad-topic"
      CAMPAIGN_PLANNED_CALL: "campaign-planned-call-topic"
      PUSH_SEND: "push-promo-topic"
      YOUR_NEW_CHANNEL: "your-new-channel-topic"  # Add your topic here
  event:
    prefix-mapping:
      MA_PUSH_DEEPLINK: "MaPushDeeplink"
      MA_PROMO_AD: "MaBanner"
      IB_PROMO_AD: "Banner"
      CAMPAIGN_PLANNED_CALL: "CampaignCall"
      PUSH_SEND: "MaPush"
      YOUR_NEW_CHANNEL: "YourEventPrefix"  # Add your event prefix here
```

### 3. Create Input Event Model

Create a new CI360 input event model in `cz.airbank.sas_agent_chstream.model.ci360`:

```java
package cz.airbank.sas_agent_chstream.model.ci360;

import com.fasterxml.jackson.annotation.JsonProperty;

public record InputEvent(
        long tenantId,
        Attributes attributes,
        String rowKey
) implements CI360Event<InputEvent.Attributes> {

    public record Attributes(
            @JsonProperty("task_id")
            String taskId,
            @JsonProperty("task_version_id")
            String taskVersionId,
            @JsonProperty("creative_content")
            String creativeContent,
            @JsonProperty("datahub_id")
            String datahubId,
            @JsonProperty("response_tracking_code")
            String responseTrackingCode,
            String timestamp,
            @JsonProperty("subject_id")
            String subjectId,
            String externalCode,
            String businessService,
            // Add other channel-specific fields here
            String yourChannelSpecificField
    ) implements EventAttributes {
        // No additional code needed - record methods auto-match interface!
    }
}
```

### 4. Import Output Event Schema

The output event schemas are imported from the `avro-schema` submodule. Ensure your output event schema exists there and is properly imported into this project. The Avro plugin will generate the Java class automatically during build.

### 5. Create Event Mapper

Create a mapper in `cz.airbank.sas_agent_chstream.mapper`:

```java
@Component
public class OutputEventMapper extends BaseMapper
        implements EventMapper<InputEvent, OutputEvent> {

    @Override
    public OutputEvent mapToOutputEvent(InputEvent inputEvent,
            Map<String, String> creativeContent, TaskCacheEntry taskCache,
            String leadId, String campaignName) {

        return OutputEvent.newBuilder()
                .setCuid(Long.parseLong(inputEvent.attributes().subjectId()))
                .setLeadId(leadId)
                .setCampaignName(campaignName)
                // Map other fields here
                .build();
    }
}
```

### 6. Create Kafka Producer

Create a producer in `cz.airbank.sas_agent_chstream.kafka.producer`:

```java
@Component
public class OutputEventKafkaProducer extends BaseEventKafkaProducer<OutputEvent> {

    public OutputEventKafkaProducer(KafkaTemplate<Long, Object> kafkaTemplate,
            ApplicationConfig applicationConfig) {
        super(kafkaTemplate, applicationConfig, AgentType.YOUR_NEW_CHANNEL);
    }
}
```

### 7. Create Agent

Create the agent in `cz.airbank.sas_agent_chstream.service.agent`:

```java
@Service
public class YourNewChannelAgent extends BaseEventProcessingAgent<
        InputEvent,
        OutputEvent,
        OutputEventKafkaProducer,
        OutputEventMapper> {

    public YourNewChannelAgent(
            OutputEventKafkaProducer producer,
            TaskCache taskCache,
            ObjectMapper objectMapper,
            SasRepository sasRepository,
            OutputEventMapper mapper) {
        super(producer, taskCache, objectMapper, sasRepository, mapper);
    }

    @Override
    public AgentType getAgentType() {
        return AgentType.YOUR_NEW_CHANNEL;
    }

    @Override
    protected Class<InputEvent> getInputEventClass() {
        return InputEvent.class;
    }
}
```

That's it! The agent will be automatically discovered and registered by Spring's `@Service` annotation.

### Key Points

1. **Input Event Interface**: Your input event must implement `CI360Event<T>` and its attributes must implement `EventAttributes`
2. **Mapper Interface**: Your mapper must implement `EventMapper<I, O>`
3. **Producer Inheritance**: Your producer must extend `BaseEventKafkaProducer<T>`
4. **Agent Inheritance**: Your agent must extend `BaseEventProcessingAgent<I, O, P, M>`
5. **Compile-Time Safety**: The compiler will force you to implement required methods, preventing runtime errors