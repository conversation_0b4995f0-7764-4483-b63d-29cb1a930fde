### Создание Avro схемы

1. Создаём джава класс
2. Устанавливаем плаги<PERSON> Dummy<PERSON>per (
   Json,Avro,GraphQL)  https://plugins.jetbrains.com/plugin/14911-dummymapper-json-avro-graphql-
3. ПКМ по Джаву классу Например src/test/java/com/glowbyte/model/Action.java
4. Выбрать Mapping Options -> Map as AVRO Schema (Jackson)
5. Создать новый файл *.avsc и вставить схему из буффера обмена например как в src/main/avro/Action.avsc

### Процедура сборки

1. Импортируем проект в IDEA
2. Project Structure -> Project Setings -> Project -> Project SDK -> Java 11.
2. File | Settings | Build, Execution, Deployment | Compiler | Annotation Processors -> True.
3. Б<PERSON>л<PERSON>и<PERSON> Gradle -> Tasks -> build -> buildDependents

4. Забираем собранный проект по пути `build/libs/integration-layer-0.0.1-SNAPSHOT.jar`


5. пример вызова java -jar integration-layer-0.0.1-SNAPSHOT.jar --task=jms --profile=sms --COMM_CD=DIRRESP
   --BATCH_SIZE=100 --CHANNEL_CODE=INF --ACTION_ID=7034 --DEBUG_MODE=TRUE


### History task
Continuously submit new data records from database to kafka in AVRO format.
When adding new AVRO specification, proceed by following these steps:

1. add the AVRO specification (FileName.avsc) into `src/main/avro` folder
2. AVSC files in src/main/avro cannot contain namespace with dash, but only with underscore
3. rebuild the `.jar` file by running commands `./gradlew build -x test` and `./gradlew jar`
4. edit the `application-history.yml` file to add new data, example below:

```
hc:
  history:
    ...
    tables:
      ...
      - name: ESP_USER.CI_CONTACT_HISTORY_JOURNAL
        journal-id-name: ESP_USER.CI_CONTACT_HISTORY_JID
        kafka-topic: sas.cdm.contact-history.v1
        header-data-schema: https://schema-registry-vn00c1.vn.infra:8081/schemas/ids/382
        header-type: net.homecredit.sas.v1.contactHistoryEvent
        class-name: net.homecredit.sas.event.cdm.contact_history.v1.ContactHistory
        mapping: "{\"eventTimestamp\":\"JOURNAL_DTTM:DateTimeLong\",\"eventId\":\"JOURNAL_ID:Long\",\"cellPackageSk\":\"CELL_PACKAGE_SK:Long\",\"contactDttm\":\"CONTACT_DTTM:DateTimeLong\",\"contactDt\":\"CONTACT_DT:DateTimeLong\",\"contactHistoryStatusCd\":\"CONTACT_HISTORY_STATUS_CD:String\",\"packageHashVal\":\"PACKAGE_HASH_VAL:String\",\"optimizationBackfillFlg\":\"OPTIMIZATION_BACKFILL_FLG:String\",\"externalContactInfoId1\":\"EXTERNAL_CONTACT_INFO_ID1:String\",\"externalContactInfoId2\":\"EXTERNAL_CONTACT_INFO_ID2:String\",\"responseTrackingCd\":\"RESPONSE_TRACKING_CD:String\",\"idCuid\":\"ID_CUID:Long\",\"identifierHashVal\":\"IDENTIFIER_HASH_VAL:String\"}"
     
```
- name: name of the table containing data in the database (including schema)
- journal-id-name: name of the table containing JOURNAL_ID info in the database (including schema)
- kafka-topic: name of kafka topic receiving the data
- header-data-schema: location of header data schema
- header-type: header used for sending data, to kafka goes as "ce_type"
- class-name: full qualified name of the generated class from AVRO specification
- mapping: JSON format specifying the mapping of database table to AVRO generated class, including datatype

