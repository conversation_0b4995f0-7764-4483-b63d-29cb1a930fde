hci:
  query:
    store-data: INSERT INTO INT_COMMUNICATION.GEN_int_q_sync (
      Status,
      GenerateDateDttm,
      Identity_id,
      Subject_id,
      Customer_id,
      Visitor_id,
      name_call_list,
      communication_start,
      communication_end,
      campaign_start,
      campaign_end,
      call_list_type,
      is_callback_request,
      LeadID,
      Rtc_id,
      Task_id,
      Task_version_id,
      Creative_id,
      message_id,
      Tpl_id
      ) VALUES ('N',sysdate,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
    store-params: INSERT INTO INT_COMMUNICATION.GEN_INT_Q_SYNC_PARAM (
      GenerateDateDttm,
      Identity_id,
      LeadID,
      Attribute_name,
      Attribute_Value
      ) VALUES (sysdate,?,?,?,?)
  ci360:
    tenant-id: 28c4dab4db000118b51cd4f2
    client-secret: MjIwMTA2MDMyMDc3bjhnMGUxZGJmbTdpYWkyZmg2bWQyYmU5OQ==
    prefix: TSO
server:
  port: 11005