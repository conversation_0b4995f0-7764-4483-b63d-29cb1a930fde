package com.homecredit.config;

import com.homecredit.enumeration.Agent;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "hci")
@Data
public class ApplicationConfig {

    private String timezone;
    private Integer cuidTimeout;
    private Integer contactIdTimeout;
    private Integer batchSize = 100; // Default batch size for database operations
    private Integer queryLimit = 10000; // Default limit for records fetched per processing cycle
    private Map<Agent, Payload> payload;
    private Map<Agent, Query> query;
    private ExtApiGwServiceConfig extapigwservice;
    private Map<Agent, RabbitConfig> rabbit;
    private Map<Agent, ThreadPoolConfig> agents;

    @Data
    public static class Payload {
        private Integer switchId;
        private String callSource;
        private String systemCode;
        private String reportLevel;
        private String sasMessageType;
        private String logicalApplication;
        private String priority;
        private String workflow;
        private String partnerID;
    }


    @Data
    public static class Query {
        private String loadCache;
        private String loadCacheCodeCallListName;
        private String getNonProcessedRecords;
        private String getBusinessAttributes;
        private String insertToErrorTable;
        private String updateDataTable;
        private String insertToCreativeTable;
    }

    @Data
    public static class ExtApiGwServiceConfig {

        private String url;
        private String token;
        private Integer readTimeout;
        private Integer connectTimeout;
    }

    @Data
    public static class RabbitConfig {
        private String routingKey;
        private String exchange;
    }

    @Data
    public static class ThreadPoolConfig {
        private Boolean enabled = true;
        private ThreadPoolSettings threadPool = new ThreadPoolSettings();
    }

    @Data
    public static class ThreadPoolSettings {
        private Integer corePoolSize = 10;
        private Integer maxPoolSize = 10;
        private Integer queueCapacity = 100;
        private Integer keepAliveSeconds = 60;
    }
}
