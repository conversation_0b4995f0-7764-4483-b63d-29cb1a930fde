package com.homecredit.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Configuration to enable Spring's async processing.
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {
    
    public AsyncConfig() {
        log.info("Async processing enabled using Spring's @EnableAsync");
    }
}
