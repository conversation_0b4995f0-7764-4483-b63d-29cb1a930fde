package com.homecredit.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.sas.mkt.agent.sdk.CI360Agent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class AgentConfiguration {

    private final ApplicationConfig applicationConfig;

    @Bean
    public ObjectMapper objectMapper() {
        return new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Bean
    public CI360Agent ci360Agent() {
        if (applicationConfig.getCi360().getProxy() != null
        && applicationConfig.getCi360().getProxy().getHost() != null
        && !applicationConfig.getCi360().getProxy().getHost().isBlank()) {
            System.setProperty("http.proxyHost", applicationConfig.getCi360().getProxy().getHost());
            System.setProperty("http.proxyPort", applicationConfig.getCi360().getProxy().getPort().toString());
        }

        return new CI360Agent(
                applicationConfig.getCi360().getGateway(),
                applicationConfig.getCi360().getTenantId(),
                applicationConfig.getCi360().getClientSecret());
    }
}
