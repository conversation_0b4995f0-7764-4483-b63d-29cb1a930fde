package com.homecredit.config;

import com.homecredit.enumeration.Agent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class AgentThreadPoolConfig {

    private final ApplicationConfig applicationConfig;

    /**
     * Helper method to create a ThreadPoolTaskExecutor with configuration from ApplicationConfig
     */
    private ThreadPoolTaskExecutor createExecutor(Agent agent, String threadNamePrefix) {
        ApplicationConfig.ThreadPoolSettings settings = applicationConfig.getAgents()
                .getOrDefault(agent, new ApplicationConfig.ThreadPoolConfig())
                .getThreadPool();
        
        log.info("Creating executor for agent {} with thread prefix '{}'", agent, threadNamePrefix);
        log.info("Thread pool configuration - Core: {}, Max: {}, Queue: {}, KeepAlive: {}s", 
                settings.getCorePoolSize(), settings.getMaxPoolSize(), 
                settings.getQueueCapacity(), settings.getKeepAliveSeconds());
        
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(settings.getCorePoolSize());
        executor.setMaxPoolSize(settings.getMaxPoolSize());
        executor.setQueueCapacity(settings.getQueueCapacity());
        executor.setKeepAliveSeconds(settings.getKeepAliveSeconds());
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        
        log.info("Executor created successfully for agent {} with thread prefix '{}'", agent, threadNamePrefix);
        return executor;
    }

    // Default executor for fallback
    @Bean("defaultExecutor")
    public Executor defaultExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("Default-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }

    @Bean("dmGenesysExecutor")
    public Executor dmGenesysExecutor() {
        return createExecutor(Agent.DM_GEN, "DMGenesys-");
    }

    @Bean("dmPushExecutor")
    public Executor dmPushExecutor() {
        return createExecutor(Agent.DM_PSH, "DMPush-");
    }

    @Bean("dmSmsExecutor")
    public Executor dmSmsExecutor() {
        return createExecutor(Agent.DM_SMS, "DMSms-");
    }

    @Bean("dmWtsExecutor")
    public Executor dmWtsExecutor() {
        return createExecutor(Agent.DM_WTS, "DMWts-");
    }

    @Bean("genesysExecutor")
    public Executor genesysExecutor() {
        return createExecutor(Agent.GEN, "Genesys-");
    }

    @Bean("inboxExecutor")
    public Executor inboxExecutor() {
        return createExecutor(Agent.IBM, "Inbox-");
    }

    @Bean("pushLmaExecutor")
    public Executor pushLmaExecutor() {
        return createExecutor(Agent.LMA, "PushLma-");
    }

    @Bean("idLmaExecutor")
    public Executor idLmaExecutor() {
        return createExecutor(Agent.IDLMA, "IdLma-");
    }

    @Bean("pushExecutor")
    public Executor pushExecutor() {
        return createExecutor(Agent.PSH, "Push-");
    }

    @Bean("smsExecutor")
    public Executor smsExecutor() {
        return createExecutor(Agent.SMS, "Sms-");
    }

    @Bean("wtsExecutor")
    public Executor wtsExecutor() {
        return createExecutor(Agent.WTS, "Wts-");
    }
}
