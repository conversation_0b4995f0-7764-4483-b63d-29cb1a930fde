package com.homecredit.model;

import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;

/**
 * Generic batch data holder for database operations across all agent channels.
 * 
 * @param <T> The message type that extends Message interface
 */
public record BatchData<T extends Message>(
        MessageStatus status,
        T message,
        String errorMessage,
        FailedStatus failedStatus) {
    
    /**
     * Constructor for successful updates without error message
     */
    public BatchData(MessageStatus status, T message) {
        this(status, message, null, null);
    }
}
