package com.homecredit.model;

/**
 * Base class for all message types in the multi-sending-agent system.
 * Provides common functionality needed for batch processing.
 */
public abstract class Message {

    /**
     * Get the unique identifier for this message.
     * This method should be implemented by all concrete message classes.
     *
     * @return the message ID
     */
    public abstract Long getId();
}
