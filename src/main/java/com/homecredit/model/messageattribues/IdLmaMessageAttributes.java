package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class IdLmaMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("contract_num")
    private String contractNum;
    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    @JsonProperty(value = "phonenumber", required = true)
    private String phoneNumber;
    @JsonProperty(value = "TASKPROP_PSH_category", required = true)
    private String taskpropPshCategory;
    @JsonProperty(value = "TASKPROP_PSH_subcategory", required = true)
    private String taskpropPshSubcategory;
    @JsonProperty(value = "TASKPROP_PSH_remark", required = true)
    private String taskpropPshRemark;
}
