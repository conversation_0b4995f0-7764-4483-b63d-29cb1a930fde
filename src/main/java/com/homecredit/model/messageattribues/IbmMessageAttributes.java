package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
public class IbmMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    @JsonProperty("login_id")
    private String loginId;
    private String sentInbox;
    private String sentPushWithPicture;
    private String sentPush;
    private String sentSms;
    @JsonProperty("SITPhoneNum")
    private String SITPhoneNum;
    @JsonProperty(value = "TASKPROP_IBM_categoryId", required = true)
    private String taskpropIbmCategoryId;
    @JsonProperty("TASKPROP_IBM_date_valid_end")
    private LocalDate taskpropIbmDateValidEnd;
    @JsonProperty("TASKPROP_IBM_date_valid_start")
    private LocalDate taskpropIbmDateValidStart;
    @JsonProperty(value = "TASKPROP_IBM_should_notify", required = true)
    private String taskpropIbmShouldNotify;
    @JsonProperty("TASKPROP_IBM_time_valid_duration")
    private String taskpropIbmTimeValidDuration;
    @JsonProperty("TASKPROP_IBM_time_valid_start")
    private String taskpropIbmTimeValidStart;
    @JsonProperty(value = "TASKPROP_IBM_validity_type", required = true)
    private String taskpropIbmValidityType;
    private String text;
}
