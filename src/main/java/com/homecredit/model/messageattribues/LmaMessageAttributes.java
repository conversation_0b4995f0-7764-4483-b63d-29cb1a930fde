package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
public class LmaMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("contributing_guid_1")
    private String contributingGuid1;
    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    private String taskName;
    @JsonProperty("TASKPROP_PSH_app")
    private String taskpropPshApp;
    @JsonProperty("TASKPROP_PSH_date_valid_end")
    private LocalDate taskPropPshDateValidEnd;
    @JsonProperty("TASKPROP_PSH_date_valid_start")
    private LocalDate taskPropPshDateValidStart;
    @JsonProperty("TASKPROP_PSH_time_valid_duration")
    private String taskpropPshTimeValidDuration;
    @JsonProperty("TASKPROP_PSH_time_valid_start")
    private String taskpropPshTimeValidStart;
    @JsonProperty(value = "TASKPROP_PSH_validity_type", required = true)
    private String taskpropPshValidityType;
    private String text;
    private String titleText;
}
