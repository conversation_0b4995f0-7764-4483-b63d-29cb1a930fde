package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BaseMessageAttributes {

    private String account;
    private String applicationId;
    @JsonProperty("customer_id")
    private String customerId;
    @JsonProperty(value = "datahub_id", required = true)
    private String datahubId;
    private String event;
    @JsonProperty("event_category")
    private String eventCategory;
    @JsonProperty("event_datetime_utc")
    private String eventDatetimeUtc;
    private String eventDesignedName;
    @JsonProperty("event_designed_name")
    private String eventDesignedName2;
    @JsonProperty("event_channel")
    private String eventChannel;
    private String eventName;
    private String eventname;
    @JsonProperty("event_uid")
    private String eventUid;
    private String externalCode;
    private String extendedCustomEventWithRevenueFlag;
    private String generatedTimestamp;
    private String guid;
    private String channelId;
    @JsonProperty("channel_user_id")
    private String channelUserId;
    @JsonProperty("channel_user_type")
    private String channelUserType;
    private String channelType;
    @JsonProperty("imprint_id")
    private String imprintId;
    private String internalTenantId;
    @JsonProperty("internal_tenant_id")
    private String internalTenantId2;
    @JsonProperty("message_id")
    private String messageId;
    @JsonProperty("parent_event")
    private String parentEvent;
    @JsonProperty("parent_eventname")
    private String parentEventname;
    @JsonProperty("parent_event_uid")
    private String parentEventUid;
    @JsonProperty(value = "response_tracking_code", required = true)
    private String responseTrackingCode;
    @JsonProperty("screen_info")
    private String screenInfo;
    private String session;
    private String sessionId;
    @JsonProperty("subject_id")
    private String subjectId;
    @JsonProperty(value = "task_id", required = true)
    private String taskId;
    @JsonProperty("TASKPROP_channel")
    private String taskpropChannel;
    @JsonProperty(value = "task_version_id", required = true)
    private String taskVersionId;
    private String timestamp;
    @JsonProperty("variant_id")
    private String variantId;
    private String vid;
}
