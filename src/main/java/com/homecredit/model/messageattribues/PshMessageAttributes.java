package com.homecredit.model.messageattribues;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString(callSuper = true)
public class PshMessageAttributes extends BaseMessageAttributes {

    @JsonProperty("creative_content")
    private String creativeContent;
    @JsonProperty(value = "creative_id", required = true)
    private String creativeId;
    @JsonProperty("creative_version_id")
    private String creativeVersionId;
    @JsonProperty("login_id")
    private String loginId;
    private String sentInbox;
    private String sentPushWithPicture;
    private String sentPush;
    private String sentSms;
    @JsonProperty("SITPhoneNum")
    private String SITPhoneNum;
    @JsonProperty("TASKPROP_PSH_categoryId")
    private String taskpropPshCategoryId;
    @JsonProperty("TASKPROP_PSH_date_valid_end")
    private LocalDate taskPropPshDateValidEnd;
    @JsonProperty("TASKPROP_PSH_date_valid_start")
    private LocalDate taskPropPshDateValidStart;
    @JsonProperty("TASKPROP_PSH_do_not_store_in_inbox")
    private String taskpropPshDoNotStoreInInbox;
    @JsonProperty("TASKPROP_PSH_time_valid_duration")
    private String taskpropPshTimeValidDuration;
    @JsonProperty("TASKPROP_PSH_time_valid_start")
    private String taskpropPshTimeValidStart;
    @JsonProperty(value = "TASKPROP_PSH_validity_type", required = true)
    private String taskpropPshValidityType;
    private String text;
}
