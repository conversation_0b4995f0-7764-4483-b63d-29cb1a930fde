package com.homecredit.service;

import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShutdownListener implements ApplicationListener<ContextClosedEvent> {

    private final StreamListener streamListener;

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        StreamListener.setExiting(true);

        try {
            streamListener.awaitShutdown();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
