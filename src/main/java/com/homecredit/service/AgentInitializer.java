package com.homecredit.service;

import com.sas.mkt.agent.sdk.CI360Agent;
import com.sas.mkt.agent.sdk.CI360AgentException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgentInitializer {

    private final StreamListener streamListener;
    private final CI360Agent agent;

    public void run() {
        try {
            log.info("Starting stream listener...");
            agent.startStream(streamListener, true);
            log.info("Started stream listener");
        } catch (CI360AgentException e) {
            log.error("CH_00 - Not possible to establish connection with tenant", e);
            throw new RuntimeException(e);
        }
    }
}
