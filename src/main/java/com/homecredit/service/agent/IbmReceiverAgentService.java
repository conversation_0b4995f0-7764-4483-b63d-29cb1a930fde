package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.IbmMessage;
import com.homecredit.model.messageattribues.IbmMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("ibm")
public class IbmReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Override
    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            IbmMessage message = mapper.readValue(eventMessage, IbmMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(IbmMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        Map<String, String> creativeContent = parseCreativeContent(attributes.getCreativeContent());
        if (creativeContentInvalid(creativeContent, attributes.getEventName())) {
            return;
        }
        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setTimestampParameter(stmt, 1, getTimeRangeMinDttm(attributes.getTaskpropIbmValidityType(), attributes.getTaskpropIbmDateValidStart(), attributes.getTaskpropIbmTimeValidStart()));
                setTimestampParameter(stmt, 2, getTimeRangeMaxDttm(attributes.getTaskpropIbmValidityType(), attributes.getTaskpropIbmDateValidEnd(), attributes.getTaskpropIbmTimeValidStart(), attributes.getTaskpropIbmTimeValidDuration()));
                setStringParameter(stmt, 3, attributes.getDatahubId());
                setStringParameter(stmt, 4, attributes.getSubjectId());
                setStringParameter(stmt, 5, attributes.getCustomerId());
                setStringParameter(stmt, 6, attributes.getVid());
                setStringParameter(stmt, 7, creativeContent.get("text"));
                setStringParameter(stmt, 8, creativeContent.get("title"));
                setStringParameter(stmt, 9, getOptionalFromCreativeContent(creativeContent, "imageUrl"));
                setStringParameter(stmt, 10, stringToBooleanInt(attributes.getTaskpropIbmShouldNotify()));
                setStringParameter(stmt, 11, removeExtraQuotes(attributes.getTaskpropIbmCategoryId()));
                setStringParameter(stmt, 12, leadId);
                setStringParameter(stmt, 13, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 14, attributes.getTaskId());
                setStringParameter(stmt, 15, attributes.getTaskVersionId());
                setStringParameter(stmt, 16, attributes.getCreativeId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }
}
