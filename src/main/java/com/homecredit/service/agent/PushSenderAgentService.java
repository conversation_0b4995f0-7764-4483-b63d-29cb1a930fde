package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.PushMessage;
import com.homecredit.service.AbstractSenderAgentService;
import com.homecredit.service.ExtApiGwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.PSH.enabled", havingValue = "true")
public class PushSenderAgentService extends AbstractSenderAgentService {

    private final RabbitTemplate rabbitTemplate;
    private final ExtApiGwService extApiGwService;

    public PushSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate,
            ExtApiGwService extApiGwService) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
        this.extApiGwService = extApiGwService;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.PSH;
    }

    public void processData() {
        List<Map<String, Object>> newRows = getNewRows(getAgent());
        for (Map<String, Object> row : newRows) {

            Long id = (Long) getColumnValue(row, "ID");
            log.debug("Processing message with ID {} ...", id);
            PushMessage message = prepareMessage(row);
            if (message.getTplId() == null) {
                log.debug("Message with ID {} has null TPL ID. Obtaining TPL ID from REST API call.", id);
                Long tplId = extApiGwService.getTplId(id, message.getCreativeId(), getAgent());
                message.setTplId(tplId);
            } else if (isMessageExpired(message)) {
                handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message is expired");
            } else if (isMessageInvalid(message)) {
                log.debug("Message with ID {} is invalid. CUID is null and no token or userId exists for prospect", message.getId());
                if (isTooLateForProcessingCuid(message)) {
                    log.debug("Message with ID {} is more than {} minutes old, marking as INVALID", message.getId(), applicationConfig.getCuidTimeout());
                    handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Message is invalid - NO TOKEN EXISTS");
                } else {
                    log.debug("Message with ID {} is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getCuidTimeout());
                }
            } else if (message.getContactId() == null) {
                log.debug("Message with ID {} has null contactId", id);
                if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                    log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                    handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes");
                } else {
                    log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                }
            } else {
                sendMessageToRabbit(message, id);
            }
        }
    }

    private void sendMessageToRabbit(PushMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            handleError(FailedStatus.INVALID, ERROR, message, "Failed to write data to JSON: " + e.getMessage());
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage());
            return;
        }
        updateDataTable(PROCESSED, message, null);
    }


    private PushMessage prepareMessage(Map<String, Object> row) {
        Long tplId = null;
        if (getColumnValue(row, "TPL_ID") != null) {
            tplId = (Long) getColumnValue(row, "TPL_ID");
        }

        PushMessage message = new PushMessage();
        message.setExternalId("PSH_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "messageCode"));
        message.setCuid((String) getColumnValue(row, "cuid"));
        message.setText((String) getColumnValue(row, "text"));
        message.setLogicalApplication("GlobalMobileApp");
        message.setEffectiveDate(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "PRIORITY_LIST"));
        if (message.getCuid() == null) {
            message.setToken((String) getColumnValue(row, "GMA_push_token"));
            message.setUserId((String) getColumnValue(row, "GMA_user_id"));
        }
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        if (tplId != null) {
            attributes.add(new Attribute("SAS_TEMPLATE_ID", tplId.toString()));
        }
        if (message.getCuid() != null) {
            attributes.add(new Attribute("CUID", message.getCuid()));
        } else {
            attributes.add(new Attribute("GMA_INBOX_USER_ID", message.getUserId()));
        }
        attributes.add(new Attribute("FCM_TITLE", (String) getColumnValue(row, "Title")));
        attributes.add(new Attribute("FCM_MUTABLE_CONTENT", "TRUE"));
        attributes.add(new Attribute("FCM_CONTENT_AVAILABLE", "TRUE"));
        attributes.add(new Attribute("SAS_LINK", (String) getColumnValue(row, "LINK")));
        attributes.add(new Attribute("GMA_DEEPLINK", (String) getColumnValue(row, "LINK")));
        attributes.add(new Attribute("GMA_CLICK_ACTION", "FLUTTER_NOTIFICATION_CLICK"));
        attributes.add(new Attribute("GMA_NOTIFICATION_TYPE", "deeplink"));
        attributes.add(new Attribute("FCM_IMAGE", (String) getColumnValue(row, "IMAGE")));
        attributes.add(new Attribute("SCREEN_LABEL", (String) getColumnValue(row, "SCREEN_LABEL")));
        if (getColumnValue(row, "IBM_categoryId") != null) {
            if (longToBoolean((Long) getColumnValue(row, "Do_not_store_in_inbox"))) {
                attributes.add(new Attribute("GMA_INBOX_DO_NOT_SAVE", "TRUE"));
            } else {
                attributes.add(new Attribute("GMA_INBOX_DO_NOT_SAVE", "FALSE"));
            }
        } else {
            if (getColumnValue(row, "GMA_INBOX") != null && getColumnValue(row, "GMA_INBOX").equals("TRUE")) {
                attributes.add(new Attribute("GMA_INBOX_DO_NOT_SAVE", "FALSE"));
            } else {
                attributes.add(new Attribute("GMA_INBOX_DO_NOT_SAVE", "TRUE"));
            }
        }
        if (getColumnValue(row, "IBM_categoryId") != null) {
            attributes.add(new Attribute("GMA_INBOX_CATEGORY", (String) getColumnValue(row, "IBM_categoryId")));
        } else {
            attributes.add(new Attribute("GMA_INBOX_CATEGORY", (String) getColumnValue(row, "GMA_INBOX_CATEGORY")));
        }
        message.setAttributes(attributes.stream().filter(it -> it.getValue() != null).collect(Collectors.toList()));
        message.setTplId(tplId);
        message.setId((Long) getColumnValue(row, "ID"));
        message.setCreativeId((String) getColumnValue(row, "CREATIVE_ID"));
        return message;
    }

    private void handleError(FailedStatus failedStatus, MessageStatus messageStatus, PushMessage message, String errorMessage) {
        log.error(errorMessage);
        log.debug("Creating record of invalid message with ID {} and status {}", message.getId(), failedStatus.toString());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
                stmt.setString(1, message.getExternalId() != null ? message.getExternalId() : "null");
                stmt.setString(2, failedStatus.toString());
                stmt.setString(3, errorMessage.substring(0, Math.min(errorMessage.length(), 36)));
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        updateDataTable(messageStatus, message, errorMessage);
    }

    private void updateDataTable(MessageStatus status, PushMessage message, String errorMessage) {
        log.debug("Updating record with ID {} to status {}", message.getId(), status.getStatus());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
                stmt.setString(1, status.getStatus());
                stmt.setString(2, message.getExternalId());
                if (message.getTplId() == null) {
                    stmt.setNull(3, Types.BIGINT);
                } else {
                    stmt.setLong(3, message.getTplId());
                }
                stmt.setString(4, message.getMessageCode());
                stmt.setString(5, errorMessage);
                stmt.setLong(6, message.getId());
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }

    private boolean isMessageExpired(PushMessage message) {
        if (message.getExpires() == null || message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        log.debug("Checking if message is expired. expires = [{}], validFrom = [{}], validTo = [{}]",
                message.getExpires(), message.getValidFrom(), message.getValidTo());
        return (message.getExpires().isBefore(message.getValidFrom()))
                || message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    private static boolean isMessageInvalid(PushMessage message) {
        log.debug("Checking if message is invalid ((token or userId is null) AND cuid is null). token = [{}], userId = [{}], cuid = [{}]",
                message.getToken(), message.getUserId(), message.getCuid());
        return (message.getToken() == null || message.getUserId() == null) && message.getCuid() == null;
    }

    private boolean isTooLateForProcessingCuid(PushMessage message) {
        ZonedDateTime zonedDateTime = message.getGeneratedDateTime();
        log.debug("Checking if message is too old old for processing cuid. generatedDateTime = [{}]", zonedDateTime.toString());
        return zonedDateTime.plusMinutes(applicationConfig.getCuidTimeout())
                .isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }
}
