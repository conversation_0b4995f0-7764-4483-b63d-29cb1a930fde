package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.GenesysMessage;
import com.homecredit.model.calllisttype.CallListName;
import com.homecredit.model.calllisttype.CallListNameCache;
import com.homecredit.model.calllisttype.CallListType;
import com.homecredit.model.calllisttype.CallListTypeCache;
import com.homecredit.model.calllisttype.CallListTypeCategory;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.GEN.enabled", havingValue = "true")
public class GenesysSenderAgentService extends AbstractSenderAgentService {

    private final KafkaTemplate<String, String> kafkaTemplate;

    public GenesysSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            KafkaTemplate<String, String> kafkaTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.kafkaTemplate = kafkaTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.GEN;
    }

    public void processData() {
        List<Map<String, Object>> newRows = getNewRows(getAgent());
        for (Map<String, Object> row : newRows) {

            Long id = (Long) getColumnValue(row, "ID");
            String callListType = (String) getColumnValue(row, "call_list_type");
            log.debug("Processing message with ID {} ...", id);

            CallListName callListName = getCallListName(row);

            String contactId = (String) getColumnValue(row, "CONTACT_ID");
            if (contactId == null) {
                if (isTooLateForProcessingContactId((ZonedDateTime) getColumnValue(row, "GenerateDateDttm"))) {
                    handleError("Message with ID " + id + " has null contact_id and is more than " + applicationConfig.getContactIdTimeout() + "  minutes old, marking as expired", "", callListName, id);
                } else {
                    log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", id, applicationConfig.getContactIdTimeout());
                }
                continue;
            }

            Set<CallListType> callListTypes = getCallListTypes(contactId, callListType, callListName, id);
            if (callListTypes == null) {
                continue;
            }
            Map<String, String> businessAttributes = getBusinessAttributes(row);
            if (!validateWithCache(row, callListTypes, businessAttributes, callListName, id)) {
                continue;
            }
            GenesysMessage message = prepareMessage(row, callListName, callListTypes, businessAttributes);

            sendMessageToKafka(message, callListName, id);
        }
    }

    private Set<CallListType> getCallListTypes(String contactId, String callListType, CallListName callListName, Long id) {
        try {
            Set<CallListType> callListTypes = CallListTypeCache.get(callListType);
            if (!callListType.equalsIgnoreCase("GENERAL")) {
                callListTypes.addAll(CallListTypeCache.get("GENERAL"));
            }
            return callListTypes;
        } catch (Exception e) {
            handleError("ERR_01 - Not supported CALL_LIST_TYPE " + callListType + " or nor found in cache", contactId, callListName, id);
            return null;
        }
    }

    private CallListName getCallListName(Map<String, Object> row) {
        if (getColumnValue(row, "tpl_id") == null) {
            return null;
        }
        Long tplId = (Long) getColumnValue(row, "tpl_id");
        return CallListNameCache.get(tplId);
    }

    private void sendMessageToKafka(GenesysMessage message, CallListName callListName, Long id) {
        String key = message.getOtherFields().get("il_communication_id").toString();
        if (key == null || key.isEmpty()) {
            log.warn("Null key - cannot send message to kafka");
            return;
        }
        log.debug("Preparing to send message with ID {} to RabbitMQ", key);

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(message);
        } catch (JsonProcessingException e) {
            handleError("Failed to write data to JSON: " + e.getMessage(), (String) message.getOtherFields().get("contact_id"), callListName, id);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(kafkaTemplate.getDefaultTopic(), key, jsonMessage);
            kafkaTemplate.send(record);
            log.info("Message sent to kafka");
        } catch (Exception e) {
            handleError("ERR_03 - Fail to sent to Kafka error " + e.getMessage(), (String) message.getOtherFields().get("contact_id"), callListName, id);
            return;
        }
        updateDataTable(PROCESSED, null, (String) message.getOtherFields().get("contact_id"), callListName, id);
    }


    private GenesysMessage prepareMessage(Map<String, Object> row, CallListName callListName, Set<CallListType> callListTypes, Map<String, String> businessAttributes) {
        Map<String, Object> messageFields = new HashMap<>();

        GenesysMessage message = new GenesysMessage();
        // Keys that should be filtered out from messageFields as they are mapped directly to records section
        Set<String> phoneNumberKeys = Set.of("CONTACT_INFO", "CLIENT_PHONE_NUMBER", "PHONE_NUMBER3", "PHONE_NUMBER4", "PHONE_NUMBER5");
        // custom mapping of phone numbers to records
        long recordType;
        if ((Long) getColumnValue(row, "is_callback_request") == 0L) {
            recordType = 2L;
        } else {
            recordType = 6L;
        }
        List<GenesysMessage.Record> records = new ArrayList<>();

        //dynamic mapping from cache
        Map<String, CallListType> callListTypeMap = callListTypes.stream().collect(Collectors.toMap(CallListType::getAttributeName, Function.identity()));
        callListTypeMap.forEach((attributeName, attribute) -> {

            if (attributeName.equalsIgnoreCase("record_type")) {
                return; // record_type is mapped to records with phone numbers, not to be included separately
            }

            // custom mapping of phone numbers
            String value = businessAttributes.get(attributeName.toUpperCase());
            if (phoneNumberKeys.contains(attributeName.toUpperCase()) && value != null) {
                GenesysMessage.Record record = new GenesysMessage.Record();
                Map<String, Object> recordFields = new HashMap<>();
                recordFields.put("contact_info", value);
                recordFields.put("record_type", recordType);
                if (recordType == 6L && attributeName.equalsIgnoreCase("CONTACT_INFO")) {
                    recordFields.put("switch_id", applicationConfig.getPayload().get(getAgent()).getSwitchId());
                }
                record.setOtherFields(recordFields);
                records.add(record);
                return;
            }

            if (attribute.getCategory() == CallListTypeCategory.BUSINESS) {
                if (attribute.getAttributeType().equals("NUMBER")) {
                    messageFields.put(attributeName, getLong(value));
                } else { // STRING, DATE
                    messageFields.put(attributeName, value);
                }
            } else {
                if (attribute.getAttributeType().equals("NUMBER")) {
                    messageFields.put(attributeName, getColumnValue(row, attributeName));
                } else if (attribute.getAttributeType().equals("DATE")) {
                    messageFields.put(attributeName, parseToString((ZonedDateTime) getColumnValue(row, attributeName)));
                } else { //STRING
                    messageFields.put(attributeName, getColumnValue(row, attributeName));
                }
            }
        });

        message.setRecords(records);

        messageFields.put("contact_id", getColumnValue(row, "contact_id"));
        messageFields.put("identity_id", getColumnValue(row, "identity_id"));
        if (getColumnValue(row, "cuid") == null) {
            messageFields.put("cuid", "");
        } else {
            messageFields.put("cuid", getColumnValue(row, "cuid"));
        }
        if (callListTypes.stream()
                .filter(it -> it.getAttributeName().equals("call_type"))
                .map(CallListType::getAttributeType)
                .findFirst()
                .orElse("")
                .equals("STRING")) {
            messageFields.put("call_type", "UNKNOWN");
        } else {
            messageFields.put("call_type", 0L);
        }
        messageFields.put("communication_start", parseToString((ZonedDateTime) getColumnValue(row, "communication_start")));
        messageFields.put("communication_end", parseToString((ZonedDateTime) getColumnValue(row, "communication_end")));
        messageFields.put("campaign_start", parseToString((ZonedDateTime) getColumnValue(row, "campaign_start")));
        messageFields.put("campaign_end", parseToString((ZonedDateTime) getColumnValue(row, "campaign_end")));
        messageFields.put("il_communication_id", getColumnValue(row, "ID"));
        messageFields.put("date_effective", parseToString(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone()))));

        if (getColumnValue(row, "tpl_id") != null) {
            messageFields.put("call_source", getColumnValue(row, "call_source"));
            if (callListName != null) {
                messageFields.put("code_call_list_type", callListName.getCodeCallListType());
                messageFields.put("name_call_list", callListName.getNameCallList());
                messageFields.put("priority", callListName.getPriority());
                messageFields.put("daily_from", callListName.getDailyFrom());
                messageFields.put("daily_till", callListName.getDailyTill());
                messageFields.put("script_type", callListName.getScriptType());
                messageFields.put("call_source", callListName.getCallSource());
                messageFields.put("call_type", callListName.getCallType());
                messageFields.put("tpl_id", callListName.getTplId());
            }
        }

        message.setOtherFields(messageFields);
        return message;
    }

    private boolean validateWithCache(Map<String, Object> row, Set<CallListType> callListTypes, Map<String, String> businessAttributes, CallListName callListName, Long id) {
        List<String> failedAttributes = new ArrayList<>();
        callListTypes.forEach(type -> {
            if (type.getCategory() == CallListTypeCategory.BUSINESS) {
                if (businessAttributes == null) {
                    failedAttributes.add(type.getAttributeName());
                } else {
                    String businessValue = businessAttributes.get(type.getAttributeName().toUpperCase());
                    if (!validateBusiness(row, type, businessValue)) {
                        failedAttributes.add(type.getAttributeName());
                    }
                }
            } else {
                if (!validateTaskOrTechnical(row, type)) {
                    failedAttributes.add(type.getAttributeName());
                }
            }
        });
        if (!failedAttributes.isEmpty()) {
            handleError("ERR_02_1 - Not possible to validate - attributes " +
                            String.join(", ", failedAttributes) + " Mandatory / wrong data type",
                    (String) getColumnValue(row, "Contact_id"), callListName, id);
            return false;
        }
        log.debug("Validation passed successfully");
        return true;
    }

    private boolean validateBusiness(Map<String, Object> row, CallListType type, String value) {
        if (value == null) {
            return !type.getMandatoryFlag();
        } else if (type.getAttributeType().equals("DATE")) {
            return getDateTime(value) != null;
        } else if (type.getAttributeType().equals("NUMBER")) {
            return getLong(value) != null;
        }
        return true;
    }

    private boolean validateTaskOrTechnical(Map<String, Object> row, CallListType type) {
        Object object = getColumnValue(row, type.getAttributeName());
        if (object == null) {
            return !type.getMandatoryFlag();
        } else if (type.getAttributeType().equals("STRING")) {
            return object instanceof String;
        } else if (type.getAttributeType().equals("DATE")) {
            return object instanceof ZonedDateTime;
        } else if (type.getAttributeType().equals("NUMBER")) {
            return object instanceof Long;
        }
        return true;
    }


    /**
     * Receive all unprocessed messages from database
     *
     * @return List of {@link GenesysMessage}
     */
    @Override
    protected List<Map<String, Object>> getNewRows(Agent agent) {
        log.debug("{} - Retrieving new rows from database ...", agent);
        List<Map<String, Object>> rows = new ArrayList<>();
        try (Connection conn = dataSource.getConnection()) {
            try (Statement statement = conn.createStatement();
                 ResultSet resultSet = statement.executeQuery(applicationConfig.getQuery().get(getAgent()).getGetNonProcessedRecords())) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (resultSet.next()) {
                    Map<String, Object> rowMap = new HashMap<>();

                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value;

                        int columnType = metaData.getColumnType(i);
                        switch (columnType) {
                            case Types.INTEGER:
                            case Types.NUMERIC:
                                value = resultSet.getLong(i);
                                break;
                            case Types.FLOAT:
                                value = resultSet.getFloat(i);
                                break;
                            case Types.DOUBLE:
                            case Types.DECIMAL:
                                value = resultSet.getDouble(i);
                                break;
                            case Types.DATE:
                            case Types.TIMESTAMP:
                                value = getDateTime(resultSet.getString(i));
                                break;
                            default:
                                value = resultSet.getString(i);
                                break;
                        }
                        rowMap.put(columnName, value);
                    }

                    // custom mapping start - validation is done before message is created
                    Set<CallListType> callListTypes = getCallListTypes(
                            (String) getColumnValue(rowMap, "CONTACT_ID"),
                            (String) getColumnValue(rowMap, "call_list_type"),
                            getCallListName(rowMap),
                            (Long) getColumnValue(rowMap, "ID"));
                    if (callListTypes.stream()
                            .filter(it -> it.getAttributeName().equals("call_type"))
                            .map(CallListType::getAttributeType)
                            .findFirst()
                            .orElse("")
                            .equals("STRING")) {
                        rowMap.put("CALL_TYPE", "UNKNOWN");
                    } else {
                        rowMap.put("CALL_TYPE", 0L);
                    }
                    if ((Long) rowMap.get("IS_CALLBACK_REQUEST") == 0L) {
                        rowMap.put("RECORD_TYPE", 2L);
                    } else {
                        rowMap.put("RECORD_TYPE", 6L);
                        rowMap.put("SWITCH_ID", applicationConfig.getPayload().get(getAgent()).getSwitchId());
                    }
                    rowMap.put("DATE_EFFECTIVE", ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
                    rowMap.put("IL_COMMUNICATION_ID", rowMap.get("ID"));
                    rowMap.put("RESPTRACKING_ID", rowMap.get("ID"));

                    // custom mapping end
                    rows.add(rowMap);
                }
            }
        } catch (SQLException e) {
            log.error("{} - Exception during executing query", agent, e);
        }

        log.debug("{} - Found {} new rows for processing", agent, rows.size());
        return rows;
    }

    private Map<String, String> getBusinessAttributes(Map<String, Object> row) {
        Map<String, String> businessAttributes = new HashMap<>();
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement statement = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getGetBusinessAttributes())) {
                statement.setString(1, (String) getColumnValue(row, "LeadID"));
                ResultSet resultSet = statement.executeQuery();
                while (resultSet.next()) {
                    String name = resultSet.getString("Attribute_name");
                    String value = resultSet.getString("Attribute_Value");
                    businessAttributes.put(name.toUpperCase(), value);
                }

            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
            return null;
        }
        return businessAttributes;
    }

    private void handleError(String errorMessage, String contactId, CallListName callListName, Long id) {
        log.error(errorMessage);
        log.debug("Creating record of invalid message with ID {}", id);
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
                stmt.setString(1, contactId);
                stmt.setString(2, errorMessage.substring(0, Math.min(errorMessage.length(), 36)));
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        updateDataTable(ERROR, errorMessage, contactId, callListName, id);
    }

    private void updateDataTable(MessageStatus status, String errorMessage, String contactId, CallListName callListName, Long id) {
        log.debug("Updating record with ID {} to status {}", id, status.getStatus());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
                stmt.setString(1, status.getStatus());
                stmt.setString(2, errorMessage);
                stmt.setString(3, contactId);
                stmt.setString(4, callListName.getCodeCallListType());
                stmt.setString(5, callListName.getNameCallList());
                stmt.setLong(6, callListName.getPriority());
                stmt.setLong(7, callListName.getDailyFrom());
                stmt.setLong(8, callListName.getDailyTill());
                stmt.setString(9, callListName.getScriptType());
                stmt.setString(10, callListName.getCallSource());
                stmt.setLong(11, id);
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }
}
