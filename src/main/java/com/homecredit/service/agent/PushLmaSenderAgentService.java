package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.MessageWrapper;
import com.homecredit.model.PushLmaMessage;
import com.homecredit.model.BatchData;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.LMA.enabled", havingValue = "true")
public class PushLmaSenderAgentService extends AbstractSenderAgentService<PushLmaMessage> {

    private final RabbitTemplate rabbitTemplate;
    private volatile boolean isProcessing = false;

    public PushLmaSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            @Qualifier("pushLmaExecutor") Executor executor,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper, executor);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        if (isProcessing) {
            return;
        }
        
        isProcessing = true;
        try {
            processData();
        } finally {
            isProcessing = false;
        }
    }

    @Override
    public Agent getAgent() {
        return Agent.LMA;
    }

    public void processData() {
        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<Map<String, Object>> newRows = getNewRows(getAgent());

            if (newRows.isEmpty()) {
                log.info("No more records to process for LMA agent. Total processed: {} records in {} batches",
                         totalProcessed, batchCount);
                break;
            }

            batchCount++;
            totalProcessed += newRows.size();
            log.info("Processing batch {} with {} records for LMA agent (total so far: {})",
                     batchCount, newRows.size(), totalProcessed);

            batchHolder.clear();

            // Process rows concurrently
            processRowsConcurrently(newRows, this::processRow);

            if (!batchHolder.isEmpty()) {
                processBatch();
            }
        }
    }

    /**
     * Process a single row - extracted for concurrent processing
     * @param row Row data to process
     * @return BatchData result or null if no processing needed
     */
    private BatchData<PushLmaMessage> processRow(Map<String, Object> row) {
        Long id = (Long) getColumnValue(row, "ID");
        log.debug("Processing message with ID {} ...", id);
        PushLmaMessage message = prepareMessage(row);

        if (isMessageExpired(message)) {
            return new BatchData<>(MessageStatus.FAILED, message, "Message is expired", FailedStatus.EXPIRED);
        } else if (isMessageInvalid(message)) {
            log.debug("Message with ID {} is invalid. CUID is null", message.getId());
            if (isTooLateForProcessingCuid(message)) {
                log.debug("Message with ID {} is more than {} minutes old, marking as INVALID", message.getId(), applicationConfig.getCuidTimeout());
                return new BatchData<>(MessageStatus.FAILED, message, "Message is invalid - NO TOKEN EXISTS", FailedStatus.INVALID);
            } else {
                log.debug("Message with ID {} is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getCuidTimeout());
                return null; // Skip processing for now
            }
        } else if (message.getContactId() == null) {
            log.debug("Message with ID {} has null contactId", id);
            if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                return new BatchData<>(MessageStatus.FAILED, message, "Message has null externalID for more than 30 minutes", FailedStatus.EXPIRED);
            } else {
                log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                return null; // Skip processing for now
            }
        } else {
            sendMessageToRabbit(message, id);
            return null; // Successfully sent, no batch data needed
        }
    }

    private void sendMessageToRabbit(PushLmaMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to write data to JSON: " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            addToBatch(MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage(), FailedStatus.INVALID);
            return;
        }
        addToBatch(PROCESSED, message);
    }


    private PushLmaMessage prepareMessage(Map<String, Object> row) {
        PushLmaMessage message = new PushLmaMessage();
        message.setExternalId("PSH_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "messageCode"));
        message.setCuid((String) getColumnValue(row, "cuid"));
        message.setText((String) getColumnValue(row, "text"));
        message.setLogicalApplication(applicationConfig.getPayload().get(getAgent()).getLogicalApplication());
        message.setEffectiveDate(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority(applicationConfig.getPayload().get(getAgent()).getPriority());
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        List<Attribute> attributes = new ArrayList<>();
        if (message.getCuid() != null) {
            attributes.add(new Attribute("CUID", message.getCuid()));
        }
        attributes.add(new Attribute("SAS_LINK", (String) getColumnValue(row, "deeplink")));
        attributes.add(new Attribute("FCM_IMAGE", (String) getColumnValue(row, "ImageURL")));
        attributes.add(new Attribute("SCREEN_LABEL", (String) getColumnValue(row, "buttonText")));
        attributes.add(new Attribute("FCM_TITLE", (String) getColumnValue(row, "Title")));
        message.setAttributes(attributes.stream().filter(it -> it.getValue() != null).collect(Collectors.toList()));

        message.setId((Long) getColumnValue(row, "ID"));
        message.setCreativeId((String) getColumnValue(row, "CREATIVE_ID"));
        return message;
    }

    @Override
    protected void processErrorInserts(Connection conn, List<BatchData<PushLmaMessage>> errorInserts) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
            for (BatchData<PushLmaMessage> data : errorInserts) {
                log.debug("Creating record of invalid message with ID {} and status {}", data.message().getId(), data.failedStatus().toString());
                stmt.setString(1, data.message().getExternalId() != null ? data.message().getExternalId() : "null");
                stmt.setString(2, data.failedStatus().toString());
                stmt.setString(3, data.errorMessage().substring(0, Math.min(data.errorMessage().length(), 36)));
                stmt.addBatch();
            }

            int[] errorResults = stmt.executeBatch();
            log.debug("Batch error insert completed. Inserted {} records", errorResults.length);
        }
    }

    @Override
    protected void processDataTableUpdates(Connection conn) throws SQLException {
        try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
            for (BatchData<PushLmaMessage> data : batchHolder) {
                log.debug("Updating record with ID {} to status {}", data.message().getId(), data.status().getStatus());
                stmt.setString(1, data.status().getStatus());
                stmt.setString(2, data.message().getExternalId());
                stmt.setString(3, data.message().getMessageCode());
                stmt.setString(4, data.errorMessage());
                stmt.setLong(5, data.message().getId());
                stmt.addBatch();
            }

            int[] updateResults = stmt.executeBatch();
            log.debug("Batch update completed. Updated {} records", updateResults.length);
        }
    }

    private boolean isMessageExpired(PushLmaMessage message) {
        if (message.getExpires() == null || message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        log.debug("Checking if message is expired. expires = [{}], validFrom = [{}], validTo = [{}]",
                message.getExpires(), message.getValidFrom(), message.getValidTo());
        return (message.getExpires().isBefore(message.getValidFrom()))
                || message.getExpires().isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    private static boolean isMessageInvalid(PushLmaMessage message) {
        log.debug("Checking if message is invalid (cuid is null). cuid = [{}]",message.getCuid());
        return message.getCuid() == null;
    }

    private boolean isTooLateForProcessingCuid(PushLmaMessage message) {
        ZonedDateTime zonedDateTime = message.getGeneratedDateTime();
        log.debug("Checking if message is too old old for processing cuid. generatedDateTime = [{}]", zonedDateTime.toString());
        return zonedDateTime.plusMinutes(applicationConfig.getCuidTimeout())
                .isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }
}
