package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.DMMessageWrapper;
import com.homecredit.model.DMPushAdditionalData;
import com.homecredit.model.DMPushMessage;
import com.homecredit.model.DMPushMessageAttribute;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.SimpleResourceHolder;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.nio.ByteBuffer;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.DM_PSH.enabled", havingValue = "true")
public class DMPushSenderAgentService extends AbstractSenderAgentService {

    private final RabbitTemplate rabbitTemplate;

    public DMPushSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    @Override
    public Agent getAgent() {
        return Agent.DM_PSH;
    }

    public void processData() {
        List<Map<String, Object>> newRows = getNewRows(getAgent());
        for (Map<String, Object> row : newRows) {

            Long id = (Long) getColumnValue(row, "ID");
            log.debug("Processing message with ID {} ...", id);
            DMPushMessage message = prepareMessage(row);
            if (message.getContactId() == null) {
                log.debug("Message with ID {} has null contactId", id);
                if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                    log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                    handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has null contactId for more than" + applicationConfig.getContactIdTimeout() + "minutes");
                } else {
                    log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                }
            } else if (message.getPhoneNumber() == null) {
                handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Message is invalid - Phone number is null");
            } else if (isMessageExpired(message)) {
                handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has expired");
            } else {
                sendMessageToRabbit(message);
            }
        }
    }

    private void sendMessageToRabbit(DMPushMessage message) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        DMMessageWrapper messageWrapper = new DMMessageWrapper();
        messageWrapper.setMessages(Collections.singletonList(message));
        messageWrapper.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        messageWrapper.setPartnerId(applicationConfig.getPayload().get(getAgent()).getPartnerID());

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            handleError(FailedStatus.INVALID, ERROR, message, "Failed to write data to JSON: " + e.getMessage());
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        SimpleResourceHolder.bind(rabbitTemplate.getConnectionFactory(), "DM_PSH");
        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", messageWrapper.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getMessageId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage());
            return;
        } finally {
            SimpleResourceHolder.unbind(rabbitTemplate.getConnectionFactory());
        }
        updateDataTable(PROCESSED, message, null);
    }


    private DMPushMessage prepareMessage(Map<String, Object> row) {
        DMPushMessage message = new DMPushMessage();
        String messageId = "SASCIPSH_" + convertToBase64((String) getColumnValue(row, "contact_id")).substring(0, 11);

        message.setPhoneNumber((String) getColumnValue(row, "PhoneNumber"));
        message.setWorkflow(applicationConfig.getPayload().get(getAgent()).getWorkflow());
        message.setMessageId(messageId);
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setLeadId((String) getColumnValue(row, "lead_id"));
        message.setIdentityId((String) getColumnValue(row, "identity_id"));
        message.setVisitorId((String) getColumnValue(row, "visitor_id"));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "generatedatedttm"))));

        DMPushMessageAttribute attribute = new DMPushMessageAttribute();
        attribute.setContractNum((String) getColumnValue(row, "contract_num"));
        attribute.setCuid((String) getColumnValue(row, "cuid"));
        attribute.setTitle((String) getColumnValue(row, "Title"));
        attribute.setFullDetailMessage((String) getColumnValue(row, "FullDetailMessage"));
        attribute.setShortMessage((String) getColumnValue(row, "ShortMessage"));
        attribute.setImageUrl((String) getColumnValue(row, "ImageURL"));
        attribute.setLongUrl((String) getColumnValue(row, "DeepLink"));

        message.setMessageAttribute(attribute);
        try {
            message.setMessageAttributeString(objectMapper.writeValueAsString(attribute));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        DMPushAdditionalData additionalData = new DMPushAdditionalData();
        //additionalData.setCategoryId((String) getColumnValue(row, "CategoryID")); //TODO where to get this, there is no column named CategoryID
        additionalData.setCategory((String) getColumnValue(row, "Category"));
        additionalData.setSubCategory((String) getColumnValue(row, "Subcategory"));
        additionalData.setRemark((String) getColumnValue(row, "Remark"));

        message.setAdditionalData(additionalData);
        try {
            message.setAdditionalDataString(objectMapper.writeValueAsString(additionalData));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        message.setId((Long) getColumnValue(row, "ID"));
        return message;
    }

    private String convertToBase64(String uuidString) {
        UUID uuid = UUID.fromString(uuidString);

        byte[] uuidBytes = ByteBuffer.wrap(new byte[16])
                .putLong(uuid.getMostSignificantBits())
                .putLong(uuid.getLeastSignificantBits())
                .array();

        return Base64.getEncoder().encodeToString(uuidBytes);
    }

    private void handleError(FailedStatus failedStatus, MessageStatus messageStatus, DMPushMessage message, String errorMessage) {
        log.error(errorMessage);
        log.debug("Creating record of invalid message with ID {} and status {}", message.getId(), failedStatus.toString());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
                stmt.setString(1, message.getContactId() != null ? message.getContactId() : "null");
                stmt.setString(2, failedStatus.toString());
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        updateDataTable(messageStatus, message, errorMessage);
    }

    private void updateDataTable(MessageStatus status, DMPushMessage message, String errorMessage) {
        log.debug("Updating record with ID {} to status {}", message.getId(), status.getStatus());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
                stmt.setString(1, status.getStatus());
                stmt.setString(2, message.getContactId());
                stmt.setString(3, message.getIdentityId());
                stmt.setString(4, message.getVisitorId());
                stmt.setString(5, message.getLeadId());
                stmt.setString(6, message.getMessageId());
                stmt.setString(7, errorMessage);
                stmt.setLong(8, message.getId());
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }

    private boolean isMessageExpired(DMPushMessage message) {
        if (message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone()));
        log.debug("Checking if message is expired. now = [{}], validFrom = [{}], validTo = [{}]",
                now, message.getValidFrom(), message.getValidTo());
        return (message.getValidFrom().isAfter(now) || message.getValidTo().isBefore(now));
    }
}
