package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.SmsMessage;
import com.homecredit.model.messageattribues.SmsMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("sms")
public class SmsReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Override
    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            SmsMessage message = mapper.readValue(eventMessage, SmsMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(SmsMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());
        LocalDateTime timeRangeMin = getTimeRangeMinDttm(attributes.getTaskpropSmsTimeValidStart());
        LocalDateTime timeRangeMax = getTimeRangeMaxDttm(timeRangeMin, attributes.getTaskpropSmsTimeValidDuration());

        String[] parsedCreativeContent = attributes.getCreativeContent().split("\\Q||\\E");
        String msgText = parsedCreativeContent[0].trim();
        String phoneNumber = Arrays.stream(parsedCreativeContent[1].split(","))
                .filter(it -> it.trim().startsWith("PHONE_NUMBER"))
                .map(it -> it.substring(it.lastIndexOf("=") + 1))
                .findFirst().orElse(null);
        if (phoneNumber == null) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload. eventName: {}. Creative content must contain phone number in format 'PHONE_NUMBER=123456789'", attributes.getEventName());
            return;
        }

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setTimestampParameter(stmt, 1, Timestamp.valueOf(timeRangeMin));
                setTimestampParameter(stmt, 2, Timestamp.valueOf(timeRangeMax));
                setTimestampParameter(stmt, 3, Timestamp.valueOf(timeRangeMax));
                setStringParameter(stmt, 4, attributes.getDatahubId());
                setStringParameter(stmt, 5, attributes.getSubjectId());
                setStringParameter(stmt, 6, attributes.getCustomerId());
                setStringParameter(stmt, 7, attributes.getVid());
                setStringParameter(stmt, 8, msgText);
                setStringParameter(stmt, 9, phoneNumber);
                setStringParameter(stmt, 10, leadId);
                setStringParameter(stmt, 11, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 12, attributes.getTaskId());
                setStringParameter(stmt, 13, attributes.getTaskVersionId());
                setStringParameter(stmt, 14, attributes.getCreativeId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }

    private LocalDateTime getTimeRangeMinDttm(String taskpropSmsTimeValidStart) {
        int validStart = Integer.parseInt(taskpropSmsTimeValidStart); //time in minutes from midnight
        if (validStart == 0) {
            return LocalDateTime.now();
        }
        if (LocalTime.of(validStart / 60, validStart % 60).isBefore(LocalTime.now())) {
            return LocalDate.now().atTime(validStart / 60, validStart % 60);
        } else {
            return LocalDateTime.now();
        }
    }

    private LocalDateTime getTimeRangeMaxDttm(LocalDateTime timeRangeMin, String taskpropSmsTimeValidDuration) {
        int validDuration = Integer.parseInt(taskpropSmsTimeValidDuration); //time in minutes
        return timeRangeMin.plusMinutes(validDuration);
    }
}
