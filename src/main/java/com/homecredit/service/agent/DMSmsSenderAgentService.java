package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.Attribute;
import com.homecredit.model.DMSmsMessage;
import com.homecredit.model.MessageWrapper;
import com.homecredit.service.AbstractSenderAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.homecredit.enumeration.MessageStatus.ERROR;
import static com.homecredit.enumeration.MessageStatus.PROCESSED;

@Slf4j
@Service
@ConditionalOnProperty(name = "hci.agents.DM_SMS.enabled", havingValue = "true")
public class DMSmsSenderAgentService extends AbstractSenderAgentService {

    private final RabbitTemplate rabbitTemplate;

    public DMSmsSenderAgentService(
            ApplicationConfig applicationConfig,
            DataSource dataSource,
            ObjectMapper objectMapper,
            RabbitTemplate rabbitTemplate) {
        super(applicationConfig, dataSource, objectMapper);
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public Agent getAgent() {
        return Agent.DM_SMS;
    }

    @Scheduled(cron = "${hci.cron.process-data}")
    public void onSchedule() {
        processData();
    }

    public void processData() {
        List<Map<String, Object>> newRows = getNewRows(getAgent());
        for (Map<String, Object> row : newRows) {

            Long id = (Long) getColumnValue(row, "ID");
            log.debug("Processing message with ID {} ...", id);
            DMSmsMessage message = prepareMessage(row);
            if (message.getContactId() == null) {
                log.debug("Message with ID {} has null contactId", id);
                if (isTooLateForProcessingContactId(message.getGeneratedDateTime())) {
                    log.debug("Message with ID {} is more than {} minutes old, marking as expired", message.getId(), applicationConfig.getContactIdTimeout());
                    handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has null contactId for more than" + applicationConfig.getContactIdTimeout() + "minutes");
                } else {
                    log.debug("Message with ID {} has null contactId, but is less than {} minutes old, will try processing later", message.getId(), applicationConfig.getContactIdTimeout());
                }
            } else if (message.getRecipient() == null) {
                handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Message is invalid - Phone number is null");
            } else if (isMessageExpired(message)) {
                handleError(FailedStatus.EXPIRED, MessageStatus.FAILED, message, "Message has expired");
            } else {
                sendMessageToRabbit(message, id);
            }
        }
    }

    private void sendMessageToRabbit(DMSmsMessage message, Long id) {
        log.debug("Preparing to send message with ID {} to RabbitMQ", message.getId());
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setMessage(Collections.singletonList(message));

        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageWrapper);
        } catch (JsonProcessingException e) {
            handleError(FailedStatus.INVALID, ERROR, message, "Failed to write data to JSON: " + e.getMessage());
            return;
        }
        log.debug("JSON message: {}", jsonMessage);

        try {
            rabbitTemplate.convertAndSend(
                    applicationConfig.getRabbit().get(getAgent()).getExchange(),
                    applicationConfig.getRabbit().get(getAgent()).getRoutingKey(),
                    jsonMessage,
                    m -> {
                        m.getMessageProperties().getHeaders().put("SYSTEM_CODE", message.getSystemCode());
                        m.getMessageProperties().getHeaders().put("REQUEST_ID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("priority", 0);
                        m.getMessageProperties().getHeaders().put("CorrelationID", message.getExternalId());
                        m.getMessageProperties().getHeaders().put("Type", "JMSType");
//                m.getMessageProperties().setPriority(0);
//                m.getMessageProperties().setCorrelationId(message.getExternalId());
//                m.getMessageProperties().setType("JMSType");
                        m.getMessageProperties().setContentType("application/json");
                        return m;
                    });
        } catch (Exception e) {
            handleError(FailedStatus.INVALID, MessageStatus.FAILED, message, "Failed to sent to RabbitMQ error " + e.getMessage());
            return;
        }
        updateDataTable(PROCESSED, message, null);
    }


    private DMSmsMessage prepareMessage(Map<String, Object> row) {
        Long tplId = null;
        if (getColumnValue(row, "TPL_ID") != null) {
            tplId = (Long) getColumnValue(row, "TPL_ID");
        }

        DMSmsMessage message = new DMSmsMessage();
        message.setExternalId("SMS_" + getColumnValue(row, "contact_id"));
        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setSystemCode(applicationConfig.getPayload().get(getAgent()).getSystemCode());
        message.setMessageCode((String) getColumnValue(row, "MESSAGE_CODE"));
        message.setRecipient((String) getColumnValue(row, "PHONE_NUMBER"));
        message.setText((String) getColumnValue(row, "MSG_TEXT"));
        message.setEffectiveDate(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setExpires(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "ExpiresOnDttm"))));
        message.setValidFrom(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMinDttm"))));
        message.setValidTo(getDateTime(parseToString((ZonedDateTime) getColumnValue(row, "TimeRangeMaxDttm"))));
        message.setGeneratedDateTime((ZonedDateTime) getColumnValue(row, "generatedatedttm"));
        message.setPriority((String) getColumnValue(row, "Priority"));
        message.setReportLevel(applicationConfig.getPayload().get(getAgent()).getReportLevel());
        message.setIsInteractive(false);

        List<Attribute> attributes = new ArrayList<>();
        attributes.add(new Attribute("SAS_MESSAGE_TYPE", applicationConfig.getPayload().get(getAgent()).getSasMessageType()));
        if (tplId != null) {
            attributes.add(new Attribute("SAS_TEMPLATE_ID", tplId.toString()));
        }
        String cuid = (String) getColumnValue(row, "cuid");
        if (cuid != null) {
            attributes.add(new Attribute("CUID", cuid));
        }
        message.setAttributes(attributes);

        message.setContactId((String) getColumnValue(row, "contact_id"));
        message.setLeadId((String) getColumnValue(row, "lead_id"));
        message.setIdentityId((String) getColumnValue(row, "identity_id"));
        message.setVisitorId((String) getColumnValue(row, "visitor_id"));
        message.setId((Long) getColumnValue(row, "ID"));
        return message;
    }

    private void handleError(FailedStatus failedStatus, MessageStatus messageStatus, DMSmsMessage message, String errorMessage) {
        log.error(errorMessage);
        log.debug("Creating record of invalid message with ID {} and status {}", message.getId(), failedStatus.toString());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getInsertToErrorTable())) {
                stmt.setString(1, message.getContactId() != null ? message.getExternalId() : "null");
                stmt.setString(2, errorMessage.substring(0, Math.min(errorMessage.length(), 36)));
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        updateDataTable(messageStatus, message, errorMessage);
    }

    private void updateDataTable(MessageStatus status, DMSmsMessage message, String errorMessage) {
        log.debug("Updating record with ID {} to status {}", message.getId(), status.getStatus());
        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(getAgent()).getUpdateDataTable())) {
                stmt.setString(1, status.getStatus());
                stmt.setString(2, message.getContactId());
                stmt.setString(3, message.getIdentityId());
                stmt.setString(4, message.getVisitorId());
                stmt.setString(5, message.getLeadId());
                stmt.setString(6, errorMessage);
                stmt.setLong(7, message.getId());
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
    }

    private boolean isMessageExpired(DMSmsMessage message) {
        if (message.getExpires() == null || message.getValidFrom() == null || message.getValidTo() == null)
            return false;
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone()));
        log.debug("Checking if message is expired. now = [{}], expires = [{}], validFrom = [{}], validTo = [{}]",
                now, message.getExpires(), message.getValidFrom(), message.getValidTo());
        return (message.getExpires().isBefore(message.getValidFrom())
                || message.getExpires().isBefore(now)
                || message.getValidFrom().isAfter(now)
                || message.getValidTo().isBefore(now));
    }
}
