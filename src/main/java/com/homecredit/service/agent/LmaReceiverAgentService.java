package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.LmaMessage;
import com.homecredit.model.messageattribues.LmaMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("lma")
public class LmaReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Override
    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            LmaMessage message = mapper.readValue(eventMessage, LmaMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(LmaMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        String status = "N";
        if (attributes.getSubjectId() == null) {
            log.error("Message has null Subject_ID (CUID), only communications to clients are supported. eventName: {}", attributes.getEventName());
            status = "F";
        }
        Map<String, String> creativeContent = parseCreativeContent(attributes.getCreativeContent());
        if (creativeContentInvalid(creativeContent, attributes.getEventName())) {
            return;
        }
        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setStringParameter(stmt, 1, status);
                setStringParameter(stmt, 2, attributes.getDatahubId());
                setStringParameter(stmt, 3, attributes.getSubjectId());
                setStringParameter(stmt, 4, attributes.getCustomerId());
                setStringParameter(stmt, 5, attributes.getVid());
                setTimestampParameter(stmt, 6, getTimeRangeMinDttm(attributes.getTaskpropPshValidityType(), attributes.getTaskPropPshDateValidStart(), attributes.getTaskpropPshTimeValidStart()));
                setTimestampParameter(stmt, 7, getTimeRangeMaxDttm(attributes.getTaskpropPshValidityType(), attributes.getTaskPropPshDateValidEnd(), attributes.getTaskpropPshTimeValidStart(), attributes.getTaskpropPshTimeValidDuration()));
                setStringParameter(stmt, 8, creativeContent.get("text"));
                setStringParameter(stmt, 9, creativeContent.get("title"));
                setStringParameter(stmt, 10, getOptionalFromCreativeContent(creativeContent, "imageURL"));
                setStringParameter(stmt, 11, getOptionalFromCreativeContent(creativeContent, "buttonLink"));
                setStringParameter(stmt, 12, getOptionalFromCreativeContent(creativeContent, "buttonText"));
                setStringParameter(stmt, 13, leadId);
                setStringParameter(stmt, 14, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 15, attributes.getTaskId());
                setStringParameter(stmt, 16, attributes.getTaskVersionId());
                setStringParameter(stmt, 17, attributes.getCreativeId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }


}
