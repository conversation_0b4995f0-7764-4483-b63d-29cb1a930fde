package com.homecredit.service.agent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.model.message.GenMessage;
import com.homecredit.model.messageattribues.GenMessageAttributes;
import com.homecredit.service.ReceiverAgentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("gen")
public class GenReceiverAgentService extends ReceiverAgentService {

    private final ApplicationConfig applicationConfig;
    private final DataSource dataSource;
    private final ObjectMapper mapper;

    @Async
    public void processEvent(String eventMessage) {
        log.info("Received event: {}", eventMessage);
        //configuration message from ci360
        if (eventMessage.startsWith("CFG")) {
            return;
        }

        try {
            GenMessage message = mapper.readValue(eventMessage, GenMessage.class);

            if (message.getAttributes() == null) {
                log.warn("Message has no 'attributes'. Cannot parse message.");
                return;
            }
            if (message.getAttributes().getEventName() == null) {
                log.warn("Message attributes have no 'eventName'. Cannot parse message.");
                return;
            }

            if (message.getAttributes().getEventName().toUpperCase().startsWith(applicationConfig.getCi360().getPrefix())) {
                log.info("Event received, will be processing. eventName: {}", message.getAttributes().getEventName());
                processMessage(message.getAttributes());
            } else {
                log.error("CH_01 - Event name {} is not matching with {}% prefix", message.getAttributes().getEventName(), applicationConfig.getCi360().getPrefix());
            }
        } catch (JsonProcessingException e) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload", e);
        }
    }

    private void processMessage(GenMessageAttributes attributes) {
        log.debug("Creating new record with data {}", attributes);

        Map<String, String> customAttributes = attributes.getOutboundProperties().entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("outboundProperties."))
                // TASKPROP fields should not be included in INT_Q_SYNC_PARAM
                .filter(entry -> !entry.getKey().contains("TASKPROP"))
                .collect(Collectors.toMap(
                        entry -> entry.getKey().substring("outboundProperties.".length()),
                        entry -> entry.getValue().equals("XNA") ? "" : entry.getValue()
                ));
        String leadId = getLeadId(attributes.getDatahubId(), attributes.getResponseTrackingCode(), attributes.getTimestamp());

        try (Connection conn = dataSource.getConnection()) {
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreData())) {
                setStringParameter(stmt, 1, attributes.getDatahubId());
                setStringParameter(stmt, 2, attributes.getSubjectId());
                setStringParameter(stmt, 3, attributes.getCustomerId());
                setStringParameter(stmt, 4, attributes.getVid());
                setStringParameter(stmt, 5, attributes.getTaskpropTsoTemplateId());
                Timestamp communicationStart = getCommunicationStart(attributes.getTaskpropTsoCommStartDays());
                Timestamp communicationEnd = getCommunicationEnd(attributes.getTaskpropTsoCommStartDays(), attributes.getTaskpropTsoCommEndDays());
                setTimestampParameter(stmt, 6, communicationStart);
                setTimestampParameter(stmt, 7, communicationEnd);
                setTimestampParameter(stmt, 8, communicationStart);
                setTimestampParameter(stmt, 9, communicationEnd);
                setStringParameter(stmt, 10, removeExtraQuotes(attributes.getTaskpropTsoCallListTypeCode()));
                setStringParameter(stmt, 11, stringToBooleanInt(attributes.getTaskpropTsoCallbackReq()));
                setStringParameter(stmt, 12, leadId);
                setStringParameter(stmt, 13, attributes.getResponseTrackingCode());
                setStringParameter(stmt, 14, attributes.getTaskId());
                setStringParameter(stmt, 15, attributes.getTaskVersionId());
                setStringParameter(stmt, 16, null);
                setStringParameter(stmt, 17, attributes.getMessageId());
                setStringParameter(stmt, 18, attributes.getTaskpropTsoTemplateId());
                stmt.executeUpdate();
            } catch (SQLException e) {
                log.error("CH_03_2 - DB - not possible to write payload to DB", e);
            }
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }

        try (Connection conn = dataSource.getConnection()) {
            customAttributes.forEach((key, value) -> {
                try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().getStoreParams())) {
                    setStringParameter(stmt, 1, attributes.getDatahubId());
                    setStringParameter(stmt, 2, leadId);
                    setStringParameter(stmt, 3, key);
                    setStringParameter(stmt, 4, value);
                    stmt.executeUpdate();
                } catch (SQLException e) {
                    log.error("CH_03_2 - DB - not possible to write payload to DB", e);
                }
            });
        } catch (SQLException e) {
            log.error("CH_03_1 - DB - not possible to establish DB connection", e);
        }
    }

    private String getDailyX(String templateId, String dailyX, String dailyXtpl) {
        if (templateId == null) {
            return dailyX;
        }
        return dailyXtpl;
    }

    private Timestamp getCommunicationStart(String daysStart) {
        return Timestamp.valueOf(LocalDateTime.now()
                .plusDays(Long.parseLong(daysStart)));
    }

    private Timestamp getCommunicationEnd(String daysStart, String daysEnd) {
        return Timestamp.valueOf(LocalDateTime.now()
                .plusDays(Long.parseLong(daysStart))
                .plusDays(Long.parseLong(daysEnd)));
    }
}
