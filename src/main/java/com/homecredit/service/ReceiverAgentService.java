package com.homecredit.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public abstract class ReceiverAgentService {

    protected static final LocalTime END_OF_DAY = LocalTime.of(23, 59, 59);

    @Async
    public abstract void processEvent(String event);

    protected static String getLeadId(String datahubId, String responseTrackingCode, String timestamp) {
        return Stream.of(datahubId, responseTrackingCode, timestamp)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("_"));
    }

    protected static Map<String, String> parseCreativeContent(String creativeContent) {
        if (creativeContent == null || creativeContent.isEmpty()) {
            return new HashMap<>();
        }
        String[] entries = creativeContent.split("\n");
        return Arrays.stream(entries)
                .map(s -> s.split("=", 2))
                .collect(Collectors.toMap(
                        a -> a[0],
                        a -> {
                            if (a.length > 1) {
                                if (a[1].equals("XNA")) {
                                    return "";
                                }
                                return a[1];
                            }
                            return "";
                        }));
    }

    protected boolean creativeContentInvalid(Map<String, String> creativeContent, String eventName) {
        if (!creativeContent.containsKey("text") || !creativeContent.containsKey("title")) {
            log.error("CH_02 - Validation error - missing mandatory attributes in payload. eventName: {}. Creative content must contain 'text', 'title'", eventName);
            return true;
        }
        return false;
    }

    protected static String getOptionalFromCreativeContent(Map<String, String> creativeContent, String key) {
        try {
            return creativeContent.get(key);
        } catch (Exception e) {
            return null;
        }
    }

    protected static String removeExtraQuotes(String originalString) {
        if (originalString == null) {
            return null;
        }
        return originalString.replaceAll("\"", "");
    }

    protected Timestamp getTimeRangeMinDttm(String validityType, LocalDate dateValidStart, String timeValidStart) {
        String cleanValidityType = removeExtraQuotes(validityType);
        if (cleanValidityType.equalsIgnoreCase("Absolute")) {
            if (dateValidStart == null) {
                return null;
            } else {
                return Timestamp.valueOf(dateValidStart.atStartOfDay());
            }
        } else if (cleanValidityType.equalsIgnoreCase("Relative")) {
            if (timeValidStart == null) {
                return null;
            } else {
                return Timestamp.valueOf(LocalDateTime.now()
                        .plusDays(Long.parseLong(timeValidStart)));
            }
        }
        log.warn("Unknown validity type: {}. Returning null TimeRangeMinDttm", validityType);
        return null;
    }

    protected Timestamp getTimeRangeMaxDttm(String validityType, LocalDate dateValidEnd, String timeValidStart, String timeValidDuration) {
        String cleanValidityType = removeExtraQuotes(validityType);
        if (cleanValidityType.equalsIgnoreCase("Absolute")) {
            if (dateValidEnd == null) {
                return null;
            } else {
                return Timestamp.valueOf(dateValidEnd.atTime(END_OF_DAY));
            }
        } else if (cleanValidityType.equalsIgnoreCase("Relative")) {
            if (timeValidDuration == null) {
                return null;
            } else if (timeValidDuration.equals("0")) {
                if (timeValidStart == null) {
                    return Timestamp.valueOf(LocalDate.now().atTime(END_OF_DAY));
                } else {
                    return Timestamp.valueOf(LocalDate.now().atTime(END_OF_DAY)
                            .plusDays(Long.parseLong(timeValidStart)));
                }
            } else {
                if (timeValidStart == null) {
                    return Timestamp.valueOf(LocalDateTime.now()
                            .plusDays(Long.parseLong(timeValidDuration)));
                } else {
                    return Timestamp.valueOf(LocalDateTime.now()
                            .plusDays(Long.parseLong(timeValidDuration))
                            .plusDays(Long.parseLong(timeValidStart)));
                }
            }
        }
        log.warn("Unknown validity type: {}. Returning null TimeRangeMaxDttm", validityType);
        return null;
    }

    protected static Integer stringToBooleanInt(String string) {
        if (string == null) {
            return 0;
        }
        return string.equalsIgnoreCase("true") ? 1 : 0;
    }

    protected static void setStringParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null || value.toString().isEmpty()) {
            st.setNull(index, Types.VARCHAR);
        } else {
            st.setString(index, value.toString());
        }
    }

    protected static void setTimestampParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.TIMESTAMP);
        } else {
            st.setTimestamp(index, (Timestamp) value);
        }
    }
}
