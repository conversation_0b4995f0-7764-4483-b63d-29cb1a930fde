package com.homecredit.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.enumeration.FailedStatus;
import com.homecredit.enumeration.MessageStatus;
import com.homecredit.model.BatchData;
import com.homecredit.model.InboxMessage;
import com.homecredit.model.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractSenderAgentService<T extends Message> {

    protected final ApplicationConfig applicationConfig;
    protected final DataSource dataSource;
    protected final ObjectMapper objectMapper;
    protected final Executor executor;

    // Batch holder for database operations
    protected final List<BatchData<T>> batchHolder = new ArrayList<>();

    public AbstractSenderAgentService(ApplicationConfig applicationConfig, 
                                    DataSource dataSource, 
                                    ObjectMapper objectMapper,
                                    @Qualifier("defaultExecutor") Executor executor) {
        this.applicationConfig = applicationConfig;
        this.dataSource = dataSource;
        this.objectMapper = objectMapper;
        this.executor = executor;
    }

    public abstract Agent getAgent();

    /**
     * Formatter for reading timestamps from Oracle DB. Allows 0-6 decimal precision for seconds.
     */
    protected static final DateTimeFormatter inputFormatter = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 0, 6, true)
            .toFormatter();

    /**
     * Formatter for outputting timestamps without milliseconds.
     */
    protected static final DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Receive all unprocessed messages from database
     *
     * @return List of {@link InboxMessage}
     */
    protected List<Map<String, Object>> getNewRows(Agent agent) {
        log.debug("{} - Retrieving new rows from database ...", agent);
        List<Map<String, Object>> rows = new ArrayList<>();

        // Build query with limit
        String baseQuery = applicationConfig.getQuery().get(getAgent()).getGetNonProcessedRecords();
        String limitedQuery = addQueryLimit(baseQuery);

        try (Connection conn = dataSource.getConnection()) {
            try (Statement statement = conn.createStatement();
                 ResultSet resultSet = statement.executeQuery(limitedQuery)) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (resultSet.next()) {
                    Map<String, Object> rowMap = new HashMap<>();

                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value;

                        int columnType = metaData.getColumnType(i);
                        if (resultSet.getObject(i) == null) {
                            value = null;
                        } else {
                            switch (columnType) {
                                case Types.INTEGER:
                                case Types.NUMERIC:
                                    value = resultSet.getLong(i);
                                    break;
                                case Types.FLOAT:
                                    value = resultSet.getFloat(i);
                                    break;
                                case Types.DOUBLE:
                                case Types.DECIMAL:
                                    value = resultSet.getDouble(i);
                                    break;
                                case Types.DATE:
                                case Types.TIMESTAMP:
                                    value = getDateTime(resultSet.getString(i));
                                    break;
                                default:
                                    value = resultSet.getString(i);
                                    break;
                            }
                        }
                        rowMap.put(columnName, value);
                    }

                    rows.add(rowMap);
                }
            }
        } catch (SQLException e) {
            log.error("{} - Exception during executing query", agent, e);
        }

        log.debug("{} - Found {} new rows for processing", agent, rows.size());
        return rows;
    }

    /**
     * Add ROWNUM limit to Oracle queries to prevent memory issues with large datasets
     */
    private String addQueryLimit(String baseQuery) {
        if (applicationConfig.getQueryLimit() == null || applicationConfig.getQueryLimit() <= 0) {
            return baseQuery;
        }

        // For Oracle queries, wrap the query with ROWNUM limit
        // This handles both simple SELECT and complex WITH clauses
        String limitedQuery = "SELECT * FROM (" + baseQuery + ") WHERE ROWNUM <= " + applicationConfig.getQueryLimit();

        log.debug("Applied query limit of {} records", applicationConfig.getQueryLimit());
        return limitedQuery;
    }

    protected boolean isTooLateForProcessingContactId(ZonedDateTime generatedDateTime) {
        log.debug("Checking if message is too old old for processing contactId. generatedDateTime = [{}]", generatedDateTime.toString());
        return generatedDateTime.plusMinutes(applicationConfig.getContactIdTimeout())
                .isBefore(ZonedDateTime.now(ZoneId.of(applicationConfig.getTimezone())));
    }

    protected ZonedDateTime getDateTime(String string) {
        try {
            return LocalDateTime.parse(string, inputFormatter)
                    .atZone(ZoneId.of(applicationConfig.getTimezone()));
        } catch (Exception e) {
            return null;
        }
    }

    protected String parseToString(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(outputFormatter);
    }

    protected Object getColumnValue(Map<String, Object> row, String columnName) {
        try {
            return row.get(columnName.toUpperCase());
        } catch (Exception e) {
            log.error("No column with name {} found.", columnName);
            throw e;
        }
    }

    protected Boolean longToBoolean(Long number) {
        return number > 0;
    }

    protected Long getLong(String value) {
        try {
            return Long.parseLong(value);
        } catch (Exception e) {
            return null;
        }
    }

    protected static void setStringParameter(PreparedStatement st, int index, Object value) throws SQLException {
        if (value == null || value.toString().isEmpty()) {
            st.setNull(index, Types.VARCHAR);
        } else {
            st.setString(index, value.toString());
        }
    }

    protected void addToBatch(MessageStatus status, T message) {
        batchHolder.add(new BatchData<>(status, message));
    }

    protected void addToBatch(MessageStatus status, T message, String errorMessage, FailedStatus failedStatus) {
        // Log error when adding failed records to batch
        if (failedStatus != null) {
            log.error("[{}] Message ID {}: {}", getAgent().name(), message.getId(), errorMessage);
        }
        batchHolder.add(new BatchData<>(status, message, errorMessage, failedStatus));
    }

    /**
     * Process the current batch of database operations
     */
    protected void processBatch() {
        if (batchHolder.isEmpty()) {
            return;
        }

        log.debug("Processing batch of {} database operations", batchHolder.size());

        // Process error inserts first (for records that need error table entries)
        List<BatchData<T>> errorInserts = batchHolder.stream()
                .filter(data -> data.failedStatus() != null)
                .toList();

        log.debug("Processing {} error inserts and {} updates", errorInserts.size(), batchHolder.size());

        // Use single connection with transaction for both operations
        try (Connection conn = dataSource.getConnection()) {
            conn.setAutoCommit(false); // Start transaction

            try {
                // Process error inserts first
                if (!errorInserts.isEmpty()) {
                    processErrorInserts(conn, errorInserts);
                }

                // Process all data table updates
                processDataTableUpdates(conn);

                conn.commit(); // Commit transaction
                log.debug("Batch transaction committed successfully for {} operations", batchHolder.size());

            } catch (SQLException e) {
                conn.rollback(); // Rollback on error
                log.error("Batch transaction rolled back due to error", e);
                throw e;
            }
        } catch (SQLException e) {
            log.error("Exception during batch database operations", e);
        }

        // Clear the batch holder after processing
        batchHolder.clear();
    }

    /**
     * Process error inserts - to be implemented by concrete classes
     * as each agent has different error insert parameters
     */
    protected abstract void processErrorInserts(Connection conn, List<BatchData<T>> errorInserts) throws SQLException;

    /**
     * Process data table updates - to be implemented by concrete classes
     * as each agent has different update parameters
     */
    protected abstract void processDataTableUpdates(Connection conn) throws SQLException;

    /**
     * Process rows concurrently using Spring's async capabilities
     * @param rows List of rows to process
     * @param rowProcessor Function to process each row
     */
    protected void processRowsConcurrently(List<Map<String, Object>> rows, RowProcessor<T> rowProcessor) {
        if (rows == null || rows.isEmpty()) {
            return;
        }

        log.info("Processing {} rows concurrently for agent {} using executor: {}", 
                rows.size(), getAgent(), executor.getClass().getSimpleName());

        // Use concurrent queue to collect results thread-safely
        ConcurrentLinkedQueue<BatchData<T>> concurrentBatch = new ConcurrentLinkedQueue<>();

        // Create async tasks for each row
        List<CompletableFuture<Void>> futures = rows.stream()
                .map(row -> processRowAsync(row, rowProcessor, concurrentBatch))
                .collect(Collectors.toList());

        // Wait for all processing to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // Add all results to batch holder
        batchHolder.addAll(concurrentBatch);
        
        log.info("Concurrent processing completed for agent {}. Added {} items to batch", 
                getAgent(), concurrentBatch.size());
    }

    /**
     * Process a single row asynchronously
     * @param row Row data to process
     * @param rowProcessor Function to process the row
     * @param concurrentBatch Queue to collect results
     * @return CompletableFuture for async processing
     */
    public CompletableFuture<Void> processRowAsync(Map<String, Object> row, RowProcessor<T> rowProcessor, 
                                                  ConcurrentLinkedQueue<BatchData<T>> concurrentBatch) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.debug("Processing row on thread: {} for agent: {}", 
                        Thread.currentThread().getName(), getAgent());
                BatchData<T> result = rowProcessor.processRow(row);
                if (result != null) {
                    concurrentBatch.add(result);
                }
            } catch (Exception e) {
                log.error("Error processing row: {}", row, e);
            }
        }, executor);
    }

    /**
     * Functional interface for processing individual rows
     * @param <T> Message type
     */
    @FunctionalInterface
    protected interface RowProcessor<T extends Message> {
        BatchData<T> processRow(Map<String, Object> row);
    }
}
