package com.homecredit.service.agent;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.homecredit.config.ApplicationConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Disabled
class WtsReceiverAgentServiceTest {

    @Mock
    private ApplicationConfig applicationConfig;

    @Mock
    private DataSource dataSource;

    @Spy
    private ObjectMapper mapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    @InjectMocks
    private WtsReceiverAgentService service;

    @BeforeEach
    void init() {
        ApplicationConfig.CI360 ci360 = new ApplicationConfig.CI360();
        ci360.setPrefix("WTS");
        ApplicationConfig.Query query = new ApplicationConfig.Query();
        query.setStoreData("storeData");
        query.setStoreParams("storeParams");
        when(applicationConfig.getCi360()).thenReturn(ci360);
        when(applicationConfig.getQuery()).thenReturn(query);
    }
    @Test
    void processEvent() throws IOException {
        Path filePath = Paths.get("src/test/resources/json/wts/01.json").toAbsolutePath();
        String jsonString = new String(Files.readAllBytes(filePath));
        service.processEvent(jsonString);
    }
}