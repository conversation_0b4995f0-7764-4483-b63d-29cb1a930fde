<?xml version="1.0" encoding="UTF-8"?>
<!-- scan this file for configuration changes every minute -->
<!-- change the scan period with: <configuration scan="true" scanPeriod="30 seconds" > -->

<configuration scan="true">

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{ISO8601} %-10.10t %-5.5p %c{16} %m%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <file>/logs/ci360/agent/lma-receiver-agent/lma-receiver-agent.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/logs/ci360/agent/lma-receiver-agent/lma-receiver-agent.log.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>600MB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <logger name="com.homecredit" level="TRACE"/>
    <logger name="com.sas.mkt.agent.sdk" level="TRACE"/>

    <root level="INFO">
        <appender-ref ref="FILE"/>
    </root>
</configuration>

