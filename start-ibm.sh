#!/bin/bash
echo

app_name="HCI IBM RECEIVER AGENT"
running=0

#test if start-ibm.pid exists and PID  is running
if test -f "./start-ibm.pid";
then
	if ps -p `cat ./start-ibm.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-ibm.xml --spring.profiles.active=ibm 2>&1 &  echo $! > start-ibm.pid
fi

echo
