#!/bin/bash
echo

app_name="HCI SMS RECEIVER AGENT"
running=0

#test if start-sms.pid exists and PID  is running
if test -f "./start-sms.pid";
then
	if ps -p `cat ./start-sms.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-sms.xml --spring.profiles.active=sms 2>&1 &  echo $! > start-sms.pid
fi

echo
