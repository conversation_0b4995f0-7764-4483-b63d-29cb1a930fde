/*
Copyright © 2021, SAS Institute Inc., Cary, NC, USA.  All Rights Reserved.
SPDX-License-Identifier: Apache-2.0
*/

create table <PREFIX>_ab_test_path_assignment(abtestpath_assignment_dttm timestamp, abtestpath_assignment_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), activity_id varchar(36), abtest_path_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), session_id_hex varchar(29));
create table <PREFIX>_activity_conversion(activity_conversion_dttm timestamp, activity_conversion_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), activity_id varchar(36), abtest_path_id varchar(36), goal_id varchar(36), channel_nm varchar(40), activity_node_id varchar(36), event_nm varchar(256), parent_event_designed_id varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_activity_flow_in(activity_flow_in_dttm timestamp, activity_flow_in_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), activity_id varchar(36), abtest_path_id varchar(36), channel_nm varchar(40), activity_node_id varchar(36), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36));
create table <PREFIX>_activity_start(activity_start_dttm timestamp, activity_start_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), activity_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36));
create table <PREFIX>_asset_details(asset_id varchar(128), asset_sk bigint, asset_nm varchar(128), asset_desc varchar(1332), asset_owner_usernm varchar(128), asset_locked_flg char(1), asset_locked_dttm timestamp, asset_locked_by_usernm varchar(128), download_disabled_flg char(1), download_disabled_dttm timestamp, download_disabled_by_usernm varchar(128), expired_dttm timestamp, expired_flg char(1), entity_status_cd varchar(3), user_rating_cnt bigint, total_user_rating_val bigint, average_user_rating_val decimal(4,2), asset_deleted_flg char(1), asset_process_status varchar(36), public_link varchar(1), public_url varchar(1024), public_media_id bigint, external_sharing_error_dt date, external_sharing_error_msg varchar(1024), folder_id varchar(128), folder_sk bigint, folder_nm varchar(128), folder_desc varchar(1332), folder_owner_usernm varchar(128), folder_path varchar(1024), folder_level int, folder_entity_status_cd varchar(3), folder_deleted_flg char(1), entity_subtype_nm varchar(128), entity_type_nm varchar(128), entity_table_nm varchar(128), entity_type_usage_cd varchar(3), entity_subtype_enabled_flg char(1), entity_revision_enabled_flg char(1), entity_attribute_enabled_flg char(1), recycled_dttm timestamp, recycled_by_usernm varchar(128), asset_source_nm varchar(128), asset_source_type varchar(128), process_id varchar(128), process_task_id varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_by_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_asset_details_custom_prop(asset_id varchar(128), attr_id varchar(128), attr_cd varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_nm varchar(128), attr_val varchar(16777216), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), is_grid_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_asset_folder_details(folder_id varchar(128), folder_nm varchar(128), folder_desc varchar(1332), folder_owner_usernm varchar(128), folder_path varchar(1024), folder_level int, entity_status_cd varchar(3), deleted_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_by_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_asset_rendition_details(rendition_id varchar(128), rendition_nm varchar(128), rendition_type_cd varchar(3), rendition_generated_type_cd varchar(3), file_nm varchar(128), file_format varchar(128), file_size int, media_dpi decimal(10,2), media_height int, media_width int, media_depth int, download_cnt bigint, rend_deleted_flg char(1), rend_duration int, last_modified_status_cd varchar(3), created_by_usernm varchar(128), created_dttm timestamp, last_modified_by_usernm varchar(128), last_modified_dttm timestamp, revision_id varchar(128), asset_id varchar(128), revision_no bigint, current_revision_flg char(1), revision_comment_txt varchar(512), entity_status_cd varchar(3), rev_deleted_flg char(1), load_dttm timestamp);
create table <PREFIX>_asset_revision(revision_id varchar(128), asset_id varchar(128), revision_no bigint, current_revision_flg char(1), revision_comment_txt varchar(512), entity_status_cd varchar(3), deleted_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_by_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_business_process_details(process_nm varchar(130), process_step_nm varchar(130), step_order_no int, process_instance_no int, process_dttm timestamp, process_attempt_cnt int, attribute1_txt varchar(130), attribute2_txt varchar(130), is_completion_flg char(1), is_start_flg char(1), process_exception_txt varchar(1300), process_exception_dttm timestamp, process_exception_dttm_tz timestamp, next_detail_id varchar(32), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, process_dttm_tz timestamp, event_designed_id varchar(36), event_nm varchar(256), process_details_sk varchar(32), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_cart_activity_details(product_nm varchar(130), product_group_nm varchar(130), product_id varchar(130), product_sku varchar(100), currency_cd varchar(6), activity_cd varchar(20), activity_dttm timestamp, activity_dttm_tz timestamp, cart_id varchar(42), displayed_cart_items_no int, displayed_cart_amt decimal(17,2), quantity_val int, unit_price_amt decimal(17,2), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), cart_activity_sk varchar(32), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), cart_nm varchar(100), availability_message_txt varchar(650), saving_message_txt varchar(650), shipping_message_txt varchar(650), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_cc_budget_breakup(planning_id varchar(128), planning_nm varchar(128), cost_center_id varchar(128), cc_nm varchar(128), cc_desc varchar(1332), cc_owner_usernm varchar(128), cc_budget_distribution decimal(17,2), cc_obsolete_flg char(1), gen_ledger_cd varchar(128), fin_accnt_nm varchar(128), fin_accnt_desc varchar(1332), fin_accnt_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_cc_budget_breakup_ccbdgt(planning_id varchar(128), planning_nm varchar(128), cost_center_id varchar(128), cc_nm varchar(128), cc_number varchar(128), cc_desc varchar(1332), cc_owner_usernm varchar(128), cc_obsolete_flg char(1), gen_ledger_cd varchar(128), fin_accnt_nm varchar(128), fin_accnt_desc varchar(1332), fin_accnt_obsolete_flg char(1), cc_lvl_distribution decimal(17,2), cc_budget_distribution decimal(17,2), cc_rldup_child_bdgt decimal(17,2), cc_level_expense decimal(17,2), cc_rldup_total_expense decimal(17,2), fp_id varchar(128), fp_nm varchar(128), fp_desc varchar(1332), fp_cls_ver varchar(128), fp_obsolete_flg char(1), fp_start_dt date, fp_end_dt date, cc_bdgt_budget_amt decimal(17,2), cc_bdgt_budget_desc varchar(1332), cc_bdgt_amt decimal(17,2), cc_bdgt_invoiced_amt decimal(17,2), cc_bdgt_committed_amt decimal(17,2), cc_bdgt_direct_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_overspent_amt decimal(17,2), cc_bdgt_cmtmnt_outstanding_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_cnt int, created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_commitment_details(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_no varchar(128), cmtmnt_nm varchar(128), cmtmnt_desc varchar(1332), cmtmnt_status varchar(128), cmtmnt_amt decimal(17,2), cmtmnt_outstanding_amt decimal(17,2), cmtmnt_payment_dttm timestamp, cmtmnt_closure_note varchar(1332), cmtmnt_overspent_amt decimal(17,2), cmtmnt_created_dttm timestamp, planning_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_amt decimal(17,2), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_commitment_line_items(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_no varchar(128), cmtmnt_nm varchar(128), cmtmnt_desc varchar(1332), cmtmnt_status varchar(128), cmtmnt_amt decimal(17,2), cmtmnt_outstanding_amt decimal(17,2), cmtmnt_payment_dttm timestamp, cmtmnt_closure_note varchar(1332), cmtmnt_overspent_amt decimal(17,2), cmtmnt_created_dttm timestamp, planning_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_amt decimal(17,2), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), cost_center_id varchar(128), cc_nm varchar(128), cc_desc varchar(1332), cc_owner_usernm varchar(128), gen_ledger_cd varchar(128), fin_acc_nm varchar(128), cc_recon_alloc_amt decimal(17,2), cc_allocated_amt decimal(17,2), cc_available_amt decimal(17,2), item_nm varchar(128), item_number int, item_qty bigint, item_rate decimal(17,2), item_alloc_unit bigint, item_alloc_amt decimal(17,2), item_vend_alloc_unit bigint, item_vend_alloc_amt decimal(17,2), ccat_nm varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_commitment_line_items_ccbdgt(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_no varchar(128), cmtmnt_nm varchar(128), cmtmnt_desc varchar(1332), cmtmnt_status varchar(128), cmtmnt_amt decimal(17,2), cmtmnt_outstanding_amt decimal(17,2), cmtmnt_payment_dttm timestamp, cmtmnt_closure_note varchar(1332), cmtmnt_overspent_amt decimal(17,2), cmtmnt_created_dttm timestamp, planning_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_amt decimal(17,2), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), cost_center_id varchar(128), cc_nm varchar(128), cc_number varchar(128), cc_desc varchar(1332), cc_obsolete_flg char(1), cc_owner_usernm varchar(128), gen_ledger_cd varchar(128), fin_acc_nm varchar(128), cc_recon_alloc_amt decimal(17,2), cc_allocated_amt decimal(17,2), cc_available_amt decimal(17,2), item_nm varchar(128), item_number int, item_qty int, item_rate decimal(17,2), item_alloc_unit int, item_alloc_amt decimal(17,2), item_vend_alloc_unit int, item_vend_alloc_amt decimal(17,2), ccat_nm varchar(128), fp_id varchar(128), fp_nm varchar(128), fp_desc varchar(1332), fp_cls_ver varchar(128), fp_obsolete_flg char(1), fp_start_dt date, fp_end_dt date, cc_bdgt_budget_amt decimal(17,2), cc_bdgt_budget_desc varchar(1332), cc_bdgt_amt decimal(17,2), cc_bdgt_invoiced_amt decimal(17,2), cc_bdgt_committed_amt decimal(17,2), cc_bdgt_direct_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_overspent_amt decimal(17,2), cc_bdgt_cmtmnt_outstanding_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_cnt int, created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_contact_history(contact_dttm timestamp, contact_dttm_tz timestamp, contact_id varchar(36), task_id varchar(36), creative_id varchar(36), message_id varchar(36), contact_channel_nm varchar(19), contact_nm varchar(256), parent_event_designed_id varchar(36), response_tracking_cd varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), occurrence_id varchar(36), task_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_conversion_milestone(conversion_milestone_dttm timestamp, conversion_milestone_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), parent_event_designed_id varchar(36), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(36), mobile_app_id varchar(40), reserved_1_txt varchar(100), goal_id varchar(36), reserved_2_txt varchar(100), total_cost_amt decimal(17,2), properties_map_doc varchar(16777216), rec_group_id varchar(3), analysis_group_id varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), activity_id varchar(36), control_group_flg char(1), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36));
create table <PREFIX>_custom_attributes(attrib_nm varchar(32), attrib_val varchar(650), attrib_dttm timestamp, attrib_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_custom_events(custom_event_nm varchar(256), custom_event_group_nm varchar(256), custom_revenue_amt decimal(17,2), custom_event_dttm timestamp, custom_event_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), event_id varchar(36), channel_user_id varchar(300), event_type_nm varchar(20), channel_nm varchar(40), mobile_app_id varchar(64), reserved_1_txt varchar(100), reserved_2_txt varchar(100), page_id varchar(256), properties_map_doc varchar(16777216), custom_events_sk varchar(32), event_key_cd varchar(100));
create table <PREFIX>_custom_events_ext(custom_events_sk varchar(32), custom_revenue_amt decimal(17,2), load_dttm timestamp, event_designed_id varchar(36));
create table <PREFIX>_daily_usage(event_day varchar(36), web_impr_cnt bigint, mob_impr_cnt bigint, web_sesn_cnt bigint, mob_sesn_cnt bigint, email_send_cnt bigint, plan_users_cnt bigint, email_preview_cnt bigint, db_size decimal(17,2), asset_size decimal(17,2), bc_subjcnt_str varchar(16777216), api_usage_str varchar(16777216));
create table <PREFIX>_data_view_details(data_view_dttm timestamp, data_view_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), reserved_1_txt varchar(100), reserved_2_txt varchar(100), total_cost_amt decimal(17,2), properties_map_doc varchar(16777216), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), parent_event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_direct_contact(direct_contact_dttm timestamp, direct_contact_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), identity_type_nm varchar(36), task_id varchar(36), message_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_document_details(uri_txt varchar(1332), alt_txt varchar(1332), link_id varchar(1332), link_name varchar(1332), link_selector_path varchar(1332), link_event_dttm timestamp, link_event_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), event_key_cd varchar(100));
create table <PREFIX>_email_bounce(email_bounce_dttm timestamp, email_bounce_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50), recipient_domain_nm varchar(100), reason_txt varchar(1000));
create table <PREFIX>_email_click(email_click_dttm timestamp, email_click_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), uri_txt varchar(1332), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), recipient_domain_nm varchar(100), link_tracking_id varchar(4), link_tracking_label_txt varchar(256), link_tracking_group_txt varchar(256), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50));
create table <PREFIX>_email_complaint(email_complaint_dttm timestamp, email_complaint_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50), recipient_domain_nm varchar(100));
create table <PREFIX>_email_open(email_open_dttm timestamp, email_open_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), recipient_domain_nm varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50), prefetched_flg char(1));
create table <PREFIX>_email_optout(email_optout_dttm timestamp, email_optout_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), recipient_domain_nm varchar(100), link_tracking_id varchar(4), link_tracking_label_txt varchar(256), link_tracking_group_txt varchar(256), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50));
create table <PREFIX>_email_optout_details(email_action_dttm timestamp, email_action_dttm_tz timestamp, event_id varchar(36), email_address varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), recipient_domain_nm varchar(100), program_id varchar(50), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(20), properties_map_doc varchar(16777216), load_dttm timestamp);
create table <PREFIX>_email_reply(email_reply_dttm timestamp, email_reply_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), uri_txt varchar(1332), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50), recipient_domain_nm varchar(100));
create table <PREFIX>_email_send(email_send_dttm timestamp, email_send_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50), recipient_domain_nm varchar(100), imprint_url_txt varchar(1332));
create table <PREFIX>_email_view(email_view_dttm timestamp, email_view_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), imprint_id varchar(36), event_nm varchar(256), event_designed_id varchar(36), identity_id varchar(36), recipient_domain_nm varchar(100), link_tracking_id varchar(4), link_tracking_label_txt varchar(256), link_tracking_group_txt varchar(256), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), load_dttm timestamp, program_id varchar(50));
create table <PREFIX>_external_event(external_event_dttm timestamp, external_event_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), channel_nm varchar(40), properties_map_doc varchar(16777216), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), response_tracking_cd varchar(36));
create table <PREFIX>_fiscal_cc_budget(cost_center_id varchar(128), cc_nm varchar(128), cc_number varchar(128), cc_desc varchar(1332), cc_owner_usernm varchar(128), cc_obsolete_flg char(1), gen_ledger_cd varchar(128), fin_accnt_nm varchar(128), fin_accnt_desc varchar(1332), fin_accnt_obsolete_flg char(1), fp_id varchar(128), fp_nm varchar(128), fp_desc varchar(1332), fp_cls_ver varchar(128), fp_obsolete_flg char(1), fp_start_dt date, fp_end_dt date, cc_bdgt_budget_amt decimal(17,2), cc_bdgt_budget_desc varchar(1332), cc_bdgt_amt decimal(17,2), cc_bdgt_invoiced_amt decimal(17,2), cc_bdgt_committed_amt decimal(17,2), cc_bdgt_direct_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_overspent_amt decimal(17,2), cc_bdgt_cmtmnt_outstanding_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_cnt int, created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_form_details(form_nm varchar(65), form_field_nm varchar(325), form_field_id varchar(325), form_field_detail_dttm timestamp, form_field_detail_dttm_tz timestamp, form_field_value varchar(2600), attempt_index_cnt int, attempt_status_cd varchar(42), change_index_no int, submit_flg char(1), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), event_key_cd varchar(100));
create table <PREFIX>_goal_details(goal_nm varchar(260), goal_group_nm varchar(130), goal_revenue_amt decimal(17,2), goal_reached_dttm timestamp, goal_reached_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), goal_details_sk varchar(32), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216));
create table <PREFIX>_goal_details_ext(goal_details_sk varchar(32), goal_revenue_amt decimal(17,2), load_dttm timestamp, event_designed_id varchar(36));
create table <PREFIX>_identity_attributes(identifier_type_id varchar(36), user_identifier_val varchar(5000), identity_id varchar(36), entrytime timestamp, processed_dttm timestamp);
create table <PREFIX>_identity_map(source_identity_id varchar(36), target_identity_id varchar(36), entrytime timestamp, processed_dttm timestamp);
create table <PREFIX>_impression_delivered(impression_delivered_dttm timestamp, impression_delivered_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), reserved_1_txt varchar(100), mobile_app_id varchar(40), reserved_2_txt varchar(100), product_nm varchar(128), product_sku_no varchar(100), product_qty_no int, identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), event_key_cd varchar(100), product_id varchar(130), control_group_flg char(1), rec_group_id varchar(3));
create table <PREFIX>_impression_spot_viewable(impression_viewable_dttm timestamp, impression_viewable_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), reserved_1_txt varchar(100), mobile_app_id varchar(40), reserved_2_txt varchar(100), product_nm varchar(128), product_sku_no varchar(100), product_qty_no int, rec_group_id varchar(3), analysis_group_id varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), control_group_flg char(1), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), event_key_cd varchar(100), product_id varchar(128));
create table <PREFIX>_in_app_failed(in_app_failed_dttm timestamp, in_app_failed_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), mobile_app_id varchar(40), error_message_txt varchar(1332), error_cd varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_in_app_message(in_app_action_dttm timestamp, in_app_action_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), reserved_3_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_in_app_send(in_app_send_dttm timestamp, in_app_send_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), mobile_app_id varchar(40), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_in_app_targeting_request(in_app_tgt_request_dttm timestamp, in_app_tgt_request_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), channel_nm varchar(40), mobile_app_id varchar(40), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), eligibility_flg char(1));
create table <PREFIX>_invoice_details(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_nm varchar(128), invoice_id varchar(128), invoice_number varchar(128), invoice_nm varchar(128), invoice_desc varchar(1332), invoice_status varchar(64), invoice_amt decimal(17,2), invoice_created_dttm timestamp, reconcile_amt decimal(17,2), reconcile_note varchar(1332), invoice_reconciled_dttm timestamp, vendor_amt decimal(17,2), payment_dttm timestamp, plan_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_desc varchar(1332), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_invoice_line_items(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_nm varchar(128), invoice_id varchar(128), invoice_number varchar(128), invoice_nm varchar(128), invoice_desc varchar(1332), invoice_status varchar(64), invoice_amt decimal(17,2), invoice_created_dttm timestamp, reconcile_amt decimal(17,2), reconcile_note varchar(1332), invoice_reconciled_dttm timestamp, vendor_amt decimal(17,2), payment_dttm timestamp, plan_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_desc varchar(1332), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), cost_center_id varchar(128), cc_nm varchar(128), cc_desc varchar(1332), cc_owner_usernm varchar(128), gen_ledger_cd varchar(128), fin_acc_nm varchar(128), fin_acc_ccat_nm varchar(128), cc_allocated_amt decimal(17,2), cc_available_amt decimal(17,2), cc_recon_alloc_amt decimal(17,2), item_nm varchar(128), item_number int, item_qty bigint, item_rate decimal(17,2), item_alloc_unit bigint, item_alloc_amt decimal(17,2), item_vend_alloc_unit bigint, item_vend_alloc_amt decimal(17,2), ccat_nm varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_invoice_line_items_ccbdgt(planning_id varchar(128), planning_nm varchar(128), cmtmnt_id varchar(128), cmtmnt_nm varchar(128), invoice_id varchar(128), invoice_number varchar(128), invoice_nm varchar(128), invoice_desc varchar(1332), invoice_status varchar(64), invoice_amt decimal(17,2), invoice_created_dttm timestamp, reconcile_amt decimal(17,2), reconcile_note varchar(1332), invoice_reconciled_dttm timestamp, vendor_amt decimal(17,2), payment_dttm timestamp, plan_currency_cd varchar(10), vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_desc varchar(1332), vendor_currency_cd varchar(10), vendor_obsolete_flg char(1), cost_center_id varchar(128), cc_nm varchar(128), cc_number varchar(128), cc_desc varchar(1332), cc_obsolete_flg char(1), cc_owner_usernm varchar(128), gen_ledger_cd varchar(128), fin_acc_nm varchar(128), fin_acc_ccat_nm varchar(128), cc_allocated_amt decimal(17,2), cc_available_amt decimal(17,2), cc_recon_alloc_amt decimal(17,2), item_nm varchar(128), item_number int, item_qty int, item_rate decimal(17,2), item_alloc_unit int, item_alloc_amt decimal(17,2), item_vend_alloc_unit int, item_vend_alloc_amt decimal(17,2), ccat_nm varchar(128), fp_id varchar(128), fp_nm varchar(128), fp_desc varchar(1332), fp_cls_ver varchar(128), fp_obsolete_flg char(1), fp_start_dt date, fp_end_dt date, cc_bdgt_budget_amt decimal(17,2), cc_bdgt_budget_desc varchar(1332), cc_bdgt_amt decimal(17,2), cc_bdgt_invoiced_amt decimal(17,2), cc_bdgt_committed_amt decimal(17,2), cc_bdgt_direct_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_amt decimal(17,2), cc_bdgt_cmtmnt_overspent_amt decimal(17,2), cc_bdgt_cmtmnt_outstanding_amt decimal(17,2), cc_bdgt_cmtmnt_invoice_cnt int, created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_activity(activity_id varchar(36), activity_nm varchar(60), activity_version_id varchar(36), activity_status_cd varchar(20), valid_from_dttm timestamp, valid_to_dttm timestamp, last_published_dttm timestamp, activity_cd varchar(60), activity_desc varchar(1332), activity_category_nm varchar(100), business_context_id varchar(36), folder_path_nm varchar(256));
create table <PREFIX>_md_activity_abtestpath(activity_id varchar(36), activity_status_cd varchar(20), activity_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, activity_node_id varchar(36), abtest_path_id varchar(36), abtest_path_nm varchar(50), abtest_dist_pct char(3), control_flg char(1), next_node_val varchar(16777216));
create table <PREFIX>_md_activity_custom_prop(activity_id varchar(36), activity_status_cd varchar(36), activity_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_cd varchar(256), property_val varchar(1332));
create table <PREFIX>_md_activity_node(activity_id varchar(36), activity_status_cd varchar(20), activity_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, activity_node_id varchar(36), activity_node_nm varchar(256), activity_node_type_nm varchar(100), start_node_flg char(1), end_node_flg char(1), previous_node_val varchar(16777216), next_node_val varchar(16777216), node_sequence_no int, wait_tm int, specific_wait_flg char(1), time_boxed_flg char(1), abtest_id varchar(36));
create table <PREFIX>_md_activity_x_activity_node(activity_id varchar(36), activity_status_cd varchar(20), activity_version_id varchar(36), activity_node_id varchar(36));
create table <PREFIX>_md_activity_x_task(activity_id varchar(36), activity_status_cd varchar(20), activity_version_id varchar(36), task_id varchar(36), task_version_id varchar(36));
create table <PREFIX>_md_asset(asset_id varchar(36), asset_nm varchar(256), asset_status_cd varchar(20), asset_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, asset_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, asset_type_nm varchar(40));
create table <PREFIX>_md_bu(bu_id varchar(128), bu_nm varchar(128), bu_desc varchar(1332), bu_obsolete_flg char(1), bu_owner_usernm varchar(128), bu_currency_cd varchar(10), bu_parentid varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_business_context(business_context_id varchar(36), business_context_nm varchar(256), business_context_status_cd varchar(20), business_context_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, business_context_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, business_context_src_cd varchar(40), information_map_nm varchar(40), locked_information_map_nm varchar(40));
create table <PREFIX>_md_cost_category(ccat_id varchar(128), ccat_nm varchar(128), ccat_desc varchar(1332), ccat_obsolete_flg char(1), fin_accnt_nm varchar(128), gen_ledger_cd varchar(128), ccat_owner_usernm varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_costcenter(cost_center_id varchar(128), cc_nm varchar(128), cc_desc varchar(1332), cc_obsolete_flg char(1), cc_owner_usernm varchar(128), fin_accnt_nm varchar(128), gen_ledger_cd varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_creative(creative_id varchar(36), creative_nm varchar(60), creative_status_cd varchar(20), creative_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, creative_desc varchar(256), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, creative_type_nm varchar(40), creative_category_nm varchar(100), creative_cd varchar(60), recommender_template_id varchar(36), recommender_template_nm varchar(60), business_context_id varchar(36), folder_path_nm varchar(256));
create table <PREFIX>_md_creative_custom_prop(creative_id varchar(36), creative_status_cd varchar(36), creative_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_cd varchar(256), property_val varchar(1332));
create table <PREFIX>_md_creative_x_asset(creative_id varchar(36), creative_status_cd varchar(20), creative_version_id varchar(36), asset_id varchar(36));
create table <PREFIX>_md_cust_attrib(attr_id varchar(128), attr_cd varchar(128), attr_nm varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), is_grid_flg char(1), associated_grid varchar(128), created_by_usernm varchar(128), last_modified_usernm varchar(128), created_dttm timestamp, last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_custattrib_table_values(attr_id varchar(128), attr_cd varchar(128), attr_nm varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_group_cd varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), table_val varchar(256), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_dataview(dataview_id varchar(36), dataview_nm varchar(60), dataview_status_cd varchar(20), dataview_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, dataview_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, analytic_active_flg char(1), include_internal_flg char(1), include_external_flg char(1), selected_task_list varchar(1000), analytics_period_val int, analytics_period_type_nm varchar(10), max_path_length_val int, max_path_time_val int, max_path_time_type_nm varchar(10), half_life_time_val int, custom_recent_cd varchar(36), custom_recent_exclude_cd varchar(36));
create table <PREFIX>_md_dataview_x_event(dataview_id varchar(36), dataview_status_cd varchar(20), dataview_version_id varchar(36), event_id varchar(36));
create table <PREFIX>_md_event(event_id varchar(36), event_nm varchar(60), event_status_cd varchar(20), event_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, event_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, event_type_nm varchar(40), event_subtype_nm varchar(100), channel_nm varchar(40));
create table <PREFIX>_md_fiscal_period(fp_id varchar(128), fp_nm varchar(128), fp_desc varchar(1332), fp_cls_ver varchar(128), fp_obsolete_flg char(1), fp_start_dt date, fp_end_dt date, created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_grid_attr_defn(grid_id varchar(128), grid_cd varchar(128), grid_nm varchar(128), grid_desc varchar(4000), grid_obsolete_flg char(1), grid_mandatory_flg char(1), attr_id varchar(128), attr_nm varchar(128), attr_cd varchar(128), attr_desc varchar(4000), attr_order_no int, associated_grid varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), attr_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_message(message_id varchar(36), message_nm varchar(60), message_status_cd varchar(20), message_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, message_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, message_type_nm varchar(40), message_category_nm varchar(100), message_cd varchar(60), business_context_id varchar(36), folder_path_nm varchar(256));
create table <PREFIX>_md_message_custom_prop(message_id varchar(36), message_status_cd varchar(36), message_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_cd varchar(256), property_val varchar(1332));
create table <PREFIX>_md_message_x_creative(message_id varchar(36), message_status_cd varchar(20), message_version_id varchar(36), creative_id varchar(36));
create table <PREFIX>_md_object_type(attr_id varchar(128), attr_cd varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_nm varchar(128), object_type varchar(64), object_category varchar(64), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_occurrence(occurrence_id varchar(36), object_id varchar(36), object_version_id varchar(36), object_type_nm varchar(100), occurrence_no int, occurrence_type_nm varchar(100), started_by_nm varchar(100), start_tm timestamp, end_tm timestamp, execution_status_cd varchar(50), properties_map_doc varchar(16777216));
create table <PREFIX>_md_picklist(attr_cd varchar(128), attr_id varchar(128), attr_nm varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), plist_id varchar(128), plist_nm varchar(128), plist_desc varchar(1332), plist_cd varchar(256), plist_val varchar(256), is_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_rtc(rtc_id varchar(36), task_id varchar(36), task_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), content_map_doc varchar(16777216), occurrence_id varchar(36), occurrence_no int, rtc_dttm timestamp);
create table <PREFIX>_md_segment(segment_id varchar(36), segment_nm varchar(60), segment_version_id varchar(36), segment_status_cd varchar(20), valid_from_dttm timestamp, valid_to_dttm timestamp, segment_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, segment_category_nm varchar(100), segment_cd varchar(36), segment_map_id varchar(36), segment_src_cd varchar(40), business_context_id varchar(36), folder_path_nm varchar(256));
create table <PREFIX>_md_segment_custom_prop(segment_id varchar(36), segment_status_cd varchar(36), segment_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_cd varchar(256), property_val varchar(1332));
create table <PREFIX>_md_segment_map(segment_map_id varchar(36), segment_map_nm varchar(60), segment_map_status_cd varchar(20), segment_map_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, segment_map_desc varchar(1332), created_user_nm varchar(60), owner_nm varchar(60), last_published_dttm timestamp, segment_map_category_nm varchar(100), segment_map_src_cd varchar(10), segment_map_cd varchar(36), scheduled_flg char(1), scheduled_start_dttm timestamp, scheduled_end_dttm timestamp, recurrence_frequency_cd varchar(36), recurrence_days_of_week_txt varchar(100), recurrence_monthly_type_nm varchar(36), recurrence_day_of_month_no int, recurrence_day_of_week_txt varchar(100), recurrence_day_of_wk_ordinal_no varchar(36), rec_scheduled_start_tm varchar(20), rec_scheduled_start_dttm timestamp, rec_scheduled_end_dttm timestamp, business_context_id varchar(36), folder_path_nm varchar(256));
create table <PREFIX>_md_segment_map_custom_prop(segment_map_id varchar(36), segment_map_status_cd varchar(36), segment_map_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_cd varchar(256), property_val varchar(1332));
create table <PREFIX>_md_segment_map_x_segment(segment_map_id varchar(36), segment_map_status_cd varchar(20), segment_map_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36));
create table <PREFIX>_md_segment_x_event(segment_id varchar(36), segment_status_cd varchar(20), segment_version_id varchar(36), event_id varchar(36));
create table <PREFIX>_md_spot(spot_id varchar(36), spot_nm varchar(60), spot_status_cd varchar(20), spot_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, spot_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, channel_nm varchar(40), spot_type_nm varchar(40), dimension_label_txt varchar(156), height_width_ratio_val_txt varchar(25), spot_height_val_no varchar(10), spot_width_val_no varchar(10), multi_page_flg char(1));
create table <PREFIX>_md_target_assist(task_id varchar(36), use_targeting_flg char(1), threshold_type_nm char(30), percent_target_population_size int, model_available_dttm timestamp, last_modified_dttm timestamp);
create table <PREFIX>_md_task(task_id varchar(36), task_nm varchar(60), task_status_cd varchar(20), task_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, task_desc varchar(1332), created_user_nm varchar(40), owner_nm varchar(40), last_published_dttm timestamp, task_type_nm varchar(40), scheduled_flg char(1), recurring_schedule_flg char(1), task_delivery_type_nm varchar(60), task_subtype_nm varchar(30), activity_flg char(1), channel_nm varchar(40), display_priority_no int, delivery_config_type_nm varchar(36), arbitration_method_cd varchar(36), task_category_nm varchar(100), task_cd varchar(60), rtdm_flg char(1), export_template_flg char(1), send_notification_locale_cd varchar(2), mobile_app_id varchar(60), mobile_app_nm varchar(60), impressions_life_time_cnt int, impressions_qty_period_cnt int, impressions_per_session_cnt int, maximum_period_expression_cnt int, limit_period_unit_cnt int, period_type_nm varchar(36), recurrence_frequency_cd varchar(36), recurrence_days_of_week_txt varchar(60), recurrence_monthly_type_nm varchar(36), recurrence_day_of_month_no int, recurrence_day_of_week_txt varchar(60), recurrence_day_of_wk_ordinal_no varchar(36), rec_scheduled_start_tm varchar(20), rec_scheduled_start_dttm timestamp, rec_scheduled_end_dttm timestamp, template_id varchar(36), subject_line_txt varchar(1332), scheduled_start_dttm timestamp, scheduled_end_dttm timestamp, business_context_id varchar(36), folder_path_nm varchar(256), use_modeling_flg char(1), model_start_dttm timestamp);
create table <PREFIX>_md_task_custom_prop(task_id varchar(36), task_status_cd varchar(36), task_version_id varchar(36), valid_from_dttm timestamp, valid_to_dttm timestamp, property_nm varchar(256), property_datatype_nm varchar(256), property_val varchar(1332));
create table <PREFIX>_md_task_x_creative(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), creative_id varchar(36), spot_id varchar(36), arbitration_method_cd varchar(36), arbitration_method_val varchar(3), variant_id varchar(36), variant_nm varchar(256));
create table <PREFIX>_md_task_x_dataview(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), dataview_id varchar(36), primary_metric_flg char(1), secondary_metric_flg char(1), targeting_flg char(1));
create table <PREFIX>_md_task_x_event(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), event_id varchar(36), primary_metric_flg char(1), secondary_metric_flg char(1), targeting_flg char(1));
create table <PREFIX>_md_task_x_message(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), message_id varchar(36));
create table <PREFIX>_md_task_x_segment(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), segment_id varchar(36));
create table <PREFIX>_md_task_x_spot(task_id varchar(36), task_status_cd varchar(20), task_version_id varchar(36), spot_id varchar(36));
create table <PREFIX>_md_vendor(vendor_id varchar(128), vendor_number varchar(128), vendor_nm varchar(128), vendor_desc varchar(1332), owner_usernm varchar(128), vendor_currency_cd varchar(10), is_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_wf_process_def(pdef_id varchar(128), engine_pdef_id varchar(128), engine_pdef_key varchar(128), pdef_nm varchar(128), pdef_desc varchar(1332), pdef_state varchar(128), pdef_type varchar(128), version_num bigint, created_by_usernm varchar(128), last_modified_usernm varchar(128), owner_usernm varchar(128), associated_object_type varchar(128), latest_version_flg char(1), buildin_template_flg char(1), default_approval_flg char(1), file_tobecatlgd_flg char(1), created_dttm timestamp, last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_md_wf_process_def_attr_grp(pdef_id varchar(128), attr_group_id varchar(128), load_dttm timestamp);
create table <PREFIX>_md_wf_process_def_categories(pdef_id varchar(128), category_id varchar(128), category_type varchar(128), default_category_flg char(1), load_dttm timestamp);
create table <PREFIX>_md_wf_process_def_task_assg(assignee_id varchar(128), assignee_type varchar(128), assignee_duration varchar(128), assignee_instruction varchar(128), pdef_id varchar(128), task_id varchar(128), load_dttm timestamp);
create table <PREFIX>_md_wf_process_def_tasks(pdef_id varchar(128), task_id varchar(128), task_nm varchar(128), task_desc varchar(1332), task_type varchar(128), task_subtype varchar(128), task_instruction varchar(128), assignee_type varchar(128), is_sequential_flg char(1), multiple_asgnsuprt_flg char(1), default_duration_perassignee bigint, outgoing_flow_flg char(1), show_sourceitemlink_flg char(1), show_workflowlink_flg char(1), predecessor_task_id varchar(128), item_approval_state varchar(128), source_item_field varchar(128), file_enabled_flg char(1), file_mandatory_flg char(1), resp_enabled_flg char(1), resp_file_enabled_flg char(1), url_enabled_flg char(1), ciobject_enabled_flg char(1), res_mandatory_flg char(1), comment_enabled_flg char(1), comment_mandatory_flg char(1), load_dttm timestamp);
create table <PREFIX>_media_activity_details(media_nm varchar(260), media_uri_txt varchar(2024), action_dttm timestamp, action_dttm_tz timestamp, action varchar(50), playhead_position varchar(50), detail_id varchar(32), detail_id_hex varchar(32), load_dttm timestamp);
create table <PREFIX>_media_details(media_nm varchar(260), media_uri_txt varchar(2024), media_player_nm varchar(30), media_player_version_txt varchar(20), play_start_dttm timestamp, play_start_dttm_tz timestamp, media_duration_secs decimal(11,3), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), event_key_cd varchar(100));
create table <PREFIX>_media_details_ext(media_nm varchar(260), media_uri_txt varchar(2024), interaction_cnt int, exit_point_secs decimal(11,3), max_play_secs decimal(11,3), view_duration_secs decimal(11,3), media_display_duration_secs decimal(11,3), play_end_dttm timestamp, play_end_dttm_tz timestamp, start_tm decimal(11,3), end_tm decimal(11,3), detail_id varchar(32), load_dttm timestamp, detail_id_hex varchar(32));
create table <PREFIX>_mobile_focus_defocus(action_dttm timestamp, action_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), reserved_1_txt varchar(100), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_mobile_spots(action_dttm timestamp, action_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), spot_id varchar(36), creative_id varchar(36), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_monthly_usage(event_month varchar(36), web_impr_cnt bigint, mob_impr_cnt bigint, web_sesn_cnt bigint, mob_sesn_cnt bigint, email_send_cnt bigint, plan_users_cnt bigint, email_preview_cnt bigint, db_size decimal(17,2), asset_size decimal(17,2), bc_subjcnt_str varchar(16777216), api_usage_str varchar(16777216));
create table <PREFIX>_notification_failed(notification_failed_dttm timestamp, notification_failed_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), event_nm varchar(256), error_message_txt varchar(1332), error_cd varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_notification_opened(notification_opened_dttm timestamp, notification_opened_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), reserved_3_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_notification_send(notification_send_dttm timestamp, notification_send_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), reserved_1_txt varchar(100), reserved_2_txt varchar(100), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_notification_targeting_request(notification_tgt_req_dttm timestamp, notification_tgt_req_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), mobile_app_id varchar(40), channel_nm varchar(40), event_nm varchar(256), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), eligibility_flg char(1));
create table <PREFIX>_order_details(product_nm varchar(130), product_group_nm varchar(130), product_id varchar(130), product_sku varchar(100), currency_cd varchar(6), record_type varchar(15), quantity_amt int, unit_price_amt decimal(17,2), availability_message_txt varchar(650), saving_message_txt varchar(650), shipping_message_txt varchar(650), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), activity_dttm timestamp, activity_dttm_tz timestamp, cart_id varchar(42), reserved_1_txt varchar(100), order_id varchar(42), cart_nm varchar(100), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_order_summary(activity_dttm timestamp, activity_dttm_tz timestamp, cart_id varchar(42), record_type varchar(15), currency_cd varchar(6), delivery_type_desc varchar(42), order_id varchar(42), payment_type_desc varchar(42), shipping_amt decimal(17,2), total_price_amt decimal(17,2), total_tax_amt decimal(17,2), shipping_country_nm varchar(85), shipping_state_region_cd varchar(256), shipping_city_nm varchar(390), shipping_postal_cd varchar(10), billing_country_nm varchar(85), billing_state_region_cd varchar(256), billing_city_nm varchar(390), billing_postal_cd varchar(10), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), cart_nm varchar(100), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_outbound_system(outbound_system_dttm timestamp, outbound_system_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), reserved_1_txt varchar(100), mobile_app_id varchar(40), reserved_2_txt varchar(100), properties_map_doc varchar(16777216), parent_event_id varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36));
create table <PREFIX>_page_details(detail_id varchar(32), session_id varchar(29), visit_id varchar(32), session_dt date, detail_dttm timestamp, detail_dttm_tz timestamp, bytes_sent_cnt int, page_load_sec_cnt int, page_complete_sec_cnt int, window_size_txt varchar(20), domain_nm varchar(165), protocol_nm varchar(8), class1_id varchar(650), class2_id varchar(650), class3_id varchar(650), class4_id varchar(650), class5_id varchar(650), class6_id varchar(650), class7_id varchar(650), class8_id varchar(650), class9_id varchar(650), class10_id varchar(650), class11_id varchar(650), class12_id varchar(650), class13_id varchar(650), class14_id varchar(650), class15_id varchar(650), page_url_txt varchar(1332), page_desc varchar(1332), url_domain varchar(215), identity_id varchar(36), load_dttm timestamp, session_dt_tz date, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), referrer_url_txt varchar(1332), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_page_details_ext(detail_id varchar(32), active_sec_spent_on_page_cnt int, seconds_spent_on_page_cnt int, session_id varchar(29), load_dttm timestamp, detail_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_page_errors(in_page_error_txt varchar(260), error_location_txt varchar(41), in_page_error_dttm timestamp, in_page_error_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_planning_hierarchy_defn(hier_defn_id varchar(128), hier_defn_nm varchar(128), hier_defn_type varchar(128), hier_defn_subtype varchar(128), hier_defn_desc varchar(1332), level_no int, level_nm varchar(128), level_desc varchar(1332), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_planning_info(planning_id varchar(128), hier_defn_id varchar(128), hier_defn_nodeid varchar(128), planning_number varchar(128), planning_nm varchar(128), planning_desc varchar(1332), planning_type varchar(32), planning_level_type varchar(32), planning_level_no varchar(10), planning_status varchar(32), planning_owner_usernm varchar(128), planning_item_path varchar(4000), planned_start_dttm timestamp, planned_end_dttm timestamp, category_nm varchar(128), all_msgs varchar(4000), currency_cd varchar(10), activity_id varchar(128), activity_nm varchar(128), activity_desc varchar(1332), activity_status varchar(128), task_id varchar(128), task_nm varchar(128), task_desc varchar(1332), task_status varchar(64), task_channel varchar(64), parent_nm varchar(128), lev1_nm varchar(128), lev2_nm varchar(128), lev3_nm varchar(128), lev4_nm varchar(128), lev5_nm varchar(128), lev6_nm varchar(128), lev7_nm varchar(128), lev8_nm varchar(128), lev9_nm varchar(128), lev10_nm varchar(128), total_budget decimal(17,2), available_budget decimal(17,2), alloc_budget decimal(17,2), reserved_budget decimal(17,2), rolledup_budget decimal(17,2), reserved_budget_same_flg char(1), tot_expenses decimal(17,2), tot_committed decimal(17,2), tot_invoiced decimal(17,2), tot_cmtmnt_outstanding decimal(17,2), tot_cmtmnt_overspent decimal(17,2), bu_id varchar(128), bu_nm varchar(128), bu_desc varchar(1332), bu_currency_cd varchar(10), bu_obsolete_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp, parent_id varchar(128));
create table <PREFIX>_planning_info_custom_prop(planning_id varchar(128), attr_id varchar(128), attr_cd varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_nm varchar(128), attr_val varchar(16777216), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), is_grid_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_product_views(product_nm varchar(130), product_group_nm varchar(130), product_id varchar(130), product_sku varchar(100), currency_cd varchar(6), price_val decimal(17,2), action_dttm timestamp, action_dttm_tz timestamp, availability_message_txt varchar(650), saving_message_txt varchar(650), shipping_message_txt varchar(650), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_promotion_displayed(promotion_nm varchar(260), promotion_tracking_cd varchar(65), promotion_type_nm varchar(65), promotion_placement_nm varchar(260), promotion_creative_nm varchar(260), promotion_number int, display_dttm timestamp, display_dttm_tz timestamp, derived_display_flg char(1), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_promotion_used(promotion_nm varchar(260), promotion_tracking_cd varchar(65), promotion_type_nm varchar(65), promotion_placement_nm varchar(260), promotion_creative_nm varchar(260), promotion_number int, click_dttm timestamp, click_dttm_tz timestamp, detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_response_history(response_dttm timestamp, response_dttm_tz timestamp, response_id varchar(36), task_id varchar(36), creative_id varchar(36), message_id varchar(36), response_channel_nm varchar(40), response_nm varchar(256), parent_event_designed_id varchar(36), response_tracking_cd varchar(36), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), occurrence_id varchar(36), task_version_id varchar(36), properties_map_doc varchar(16777216));
create table <PREFIX>_search_results(search_nm varchar(42), search_results_dttm timestamp, search_results_dttm_tz timestamp, results_displayed_flg char(1), search_results_displayed int, srch_phrase varchar(2600), srch_field_name varchar(325), srch_field_id varchar(325), detail_id varchar(32), visit_id varchar(32), session_id varchar(29), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), search_results_sk varchar(100), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), properties_map_doc varchar(16777216), event_key_cd varchar(100), channel_nm varchar(40), mobile_app_id varchar(40));
create table <PREFIX>_search_results_ext(search_results_sk varchar(100), search_results_displayed int, load_dttm timestamp, event_designed_id varchar(36));
create table <PREFIX>_session_details(browser_nm varchar(52), browser_version_no varchar(16), country_nm varchar(85), country_cd varchar(2), state_region_cd varchar(2), region_nm varchar(256), city_nm varchar(390), latitude decimal(13,6), longitude decimal(13,6), ip_address varchar(64), organization_nm varchar(256), postal_cd varchar(13), metro_cd int, device_nm varchar(85), device_type_nm varchar(32), platform_desc varchar(78), platform_type_nm varchar(52), profile_nm1 varchar(169), profile_nm2 varchar(169), profile_nm3 varchar(169), profile_nm4 varchar(169), profile_nm5 varchar(169), session_id varchar(29), session_dt date, session_start_dttm timestamp, session_start_dttm_tz timestamp, client_session_start_dttm timestamp, client_session_start_dttm_tz timestamp, previous_session_id varchar(29), flash_version_no varchar(16), flash_enabled_flg char(1), java_version_no varchar(12), java_enabled_flg char(1), java_script_enabled_flg char(1), cookies_enabled_flg char(1), user_language_cd varchar(12), screen_color_depth_no int, screen_size_txt varchar(12), user_agent_nm varchar(512), new_visitor_flg varchar(2), session_timeout int, visitor_id varchar(32), identity_id varchar(36), load_dttm timestamp, session_id_hex varchar(29), previous_session_id_hex varchar(29), session_dt_tz date, channel_nm varchar(40), app_id varchar(36), app_version varchar(10), sdk_version varchar(25), platform_version varchar(25), device_language varchar(12), manufacturer varchar(75), mobile_country_code varchar(10), network_code varchar(10), carrier_name varchar(36), is_portable_flag char(1));
create table <PREFIX>_session_details_ext(session_id varchar(29), active_sec_spent_in_sessn_cnt int, last_session_activity_dttm timestamp, last_session_activity_dttm_tz timestamp, seconds_spent_in_session_cnt int, load_dttm timestamp, session_expiration_dttm timestamp, session_expiration_dttm_tz timestamp, session_id_hex varchar(29));
create table <PREFIX>_spot_clicked(spot_clicked_dttm timestamp, spot_clicked_dttm_tz timestamp, event_id varchar(36), channel_user_id varchar(300), task_id varchar(36), spot_id varchar(36), creative_id varchar(36), channel_nm varchar(40), reserved_1_txt varchar(100), mobile_app_id varchar(40), reserved_2_txt varchar(100), product_nm varchar(128), product_sku_no varchar(100), product_qty_no int, product_id varchar(128), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), event_nm varchar(256), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29), occurrence_id varchar(36), response_tracking_cd varchar(36), task_version_id varchar(36), message_id varchar(36), message_version_id varchar(36), creative_version_id varchar(36), segment_id varchar(36), segment_version_id varchar(36), properties_map_doc varchar(16777216), event_key_cd varchar(100), control_group_flg char(1), rec_group_id varchar(3));
create table <PREFIX>_spot_requested(spot_requested_dttm timestamp, spot_requested_dttm_tz timestamp, event_id varchar(36), event_nm varchar(256), channel_user_id varchar(300), spot_id varchar(36), channel_nm varchar(40), mobile_app_id varchar(40), identity_id varchar(36), load_dttm timestamp, event_designed_id varchar(36), detail_id_hex varchar(32), visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_tag_details(tag_id varchar(128), identity_cd varchar(128), component_id varchar(128), component_type varchar(32), tag_nm varchar(128), tag_desc varchar(1332), tag_owner_usernm varchar(128), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_visit_details(visit_id varchar(32), origination_nm varchar(260), origination_type_nm varchar(65), origination_placement_nm varchar(390), origination_creative_nm varchar(260), origination_tracking_cd varchar(65), referrer_domain_nm varchar(215), referrer_txt varchar(1332), search_engine_domain_txt varchar(215), search_engine_desc varchar(130), search_term_txt varchar(1332), session_id varchar(29), visit_dttm timestamp, visit_dttm_tz timestamp, sequence_no int, referrer_query_string_txt varchar(1332), identity_id varchar(36), load_dttm timestamp, visit_id_hex varchar(32), session_id_hex varchar(29));
create table <PREFIX>_wf_process_details(process_id varchar(128), pdef_id varchar(128), process_nm varchar(128), process_desc varchar(1332), process_status varchar(128), process_comment varchar(128), process_category varchar(128), process_type varchar(128), process_instance_version varchar(128), modified_status_cd varchar(128), business_info_id varchar(128), business_info_nm varchar(128), business_info_type varchar(128), user_tasks_cnt bigint, delayed_by_day bigint, percent_complete bigint, process_owner_usernm varchar(128), submitted_by_usernm varchar(128), created_by_usernm varchar(128), deleted_by_usernm varchar(128), published_by_usernm varchar(128), last_modified_usernm varchar(128), completed_dttm timestamp, created_dttm timestamp, deleted_dttm timestamp, indexed_dttm timestamp, start_dttm timestamp, planned_end_dttm timestamp, projected_end_dttm timestamp, published_dttm timestamp, submitted_dttm timestamp, last_modified_dttm timestamp, timeline_calculated_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_wf_process_details_custom_prop(process_id varchar(128), attr_id varchar(128), attr_cd varchar(128), attr_group_id varchar(128), attr_group_nm varchar(128), attr_nm varchar(128), attr_val varchar(16777216), attr_group_cd varchar(128), remote_pklist_tab_col varchar(128), is_obsolete_flg char(1), data_type varchar(32), data_formatter varchar(64), is_grid_flg char(1), created_by_usernm varchar(128), created_dttm timestamp, last_modified_usernm varchar(128), last_modified_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_wf_process_tasks(task_id varchar(128), process_id varchar(128), engine_taskdef_id varchar(128), task_nm varchar(128), task_desc varchar(1332), task_type varchar(128), task_subtype varchar(128), task_status varchar(128), task_comment varchar(128), task_instruction varchar(128), task_attachment varchar(128), created_by_usernm varchar(128), deleted_by_usernm varchar(128), modified_by_usernm varchar(128), published_by_usernm varchar(128), owner_usernm varchar(128), modified_status_cd varchar(128), version_num bigint, instance_version varchar(128), duration_per_assignee bigint, delayed_by_day bigint, percent_complete bigint, is_sequential_flg char(1), approval_task_flg char(1), first_usertask_flg char(1), cancelled_task_flg char(1), latest_flg char(1), locally_updated_flg char(1), existobj_update_flg char(1), skip_peerupdate_scanning_flg char(1), skip_update_scanning_flg char(1), multi_assig_suprt_flg char(1), completed_dttm timestamp, created_dttm timestamp, deleted_dttm timestamp, indexed_dttm timestamp, modified_dttm timestamp, projected_end_dttm timestamp, projected_start_dttm timestamp, published_dttm timestamp, started_dttm timestamp, due_dttm timestamp, engine_task_cancelled_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_wf_tasks_user_assignment(user_assignment_id varchar(128), task_id varchar(128), user_id varchar(128), process_id varchar(128), user_nm varchar(128), approval_status varchar(128), assignee_id varchar(128), assignee_type varchar(128), usan_comment varchar(128), usan_desc varchar(1332), usan_status varchar(128), initiator_comment varchar(128), instance_version varchar(128), usan_instruction varchar(128), modified_status_cd varchar(128), activation_completed_flg char(1), is_assigned_flg char(1), is_latest_flg char(1), is_replaced_flg char(1), replacement_assignee_id varchar(128), replacement_reason varchar(128), replacement_userid varchar(128), created_by_usernm varchar(128), deleted_by_usernm varchar(128), modified_by_usernm varchar(128), owner_usernm varchar(128), delayed_by_day bigint, usan_duration_day bigint, completed_dttm timestamp, created_dttm timestamp, deleted_dttm timestamp, due_dttm timestamp, modified_dttm timestamp, projected_end_dttm timestamp, projected_start_dttm timestamp, start_dttm timestamp, load_dttm timestamp);
create table <PREFIX>_abt_attribution(identity_id varchar(36), interaction_type varchar(15), interaction_id varchar(36), interaction varchar(260), interaction_subtype varchar(100), interaction_dttm timestamp, task_id varchar(36), conversion_value int, interaction_cost int, load_id varchar(36), creative_id varchar(36));