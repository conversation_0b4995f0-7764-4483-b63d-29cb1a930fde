{"type": "record", "name": "SendPushDeeplinkEvent", "namespace": "cz.airbank.sas.campaign.push.deeplink", "fields": [{"name": "cuid", "type": "long"}, {"name": "creator", "type": "string"}, {"name": "externalId", "type": "string"}, {"name": "priority", "type": "int"}, {"name": "displayGroup", "type": "string"}, {"name": "communicationKind", "type": "string"}, {"name": "products", "type": ["null", {"type": "array", "items": "string"}], "default": null}, {"name": "taskVersionId", "type": "string"}, {"name": "campaignCode", "type": "string"}, {"name": "campaignName", "type": "string"}, {"name": "communicationCode", "type": "string"}, {"name": "communicationName", "type": "string"}, {"name": "businessSummaryCauseCode", "type": ["null", "string"], "default": null}, {"name": "businessService", "type": ["null", "string"], "default": null}, {"name": "alert", "type": "string"}, {"name": "title", "type": "string"}, {"name": "url", "type": "string"}, {"name": "created", "type": {"type": "string", "logicalType": "iso-zoned-date-time"}}]}