#!/bin/bash
echo

app_name="HCI PSH RECEIVER AGENT"
running=0

#test if start-psh.pid exists and PID  is running
if test -f "./start-psh.pid";
then
	if ps -p `cat ./start-psh.pid` > /dev/null
	then
		running=1
		echo "$app_name is already running !"
	fi
fi



if [ "$running" == "0" ]
then
	#start application
	echo Starting $app_name

	java -jar ./multi-receiver-agent.jar --logging.config=./logback-psh.xml --spring.profiles.active=psh 2>&1 &  echo $! > start-psh.pid
fi

echo
