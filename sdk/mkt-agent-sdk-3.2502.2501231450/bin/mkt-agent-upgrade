#!/usr/bin/env bash

##############################################################################
##
##  mkt-agent-upgrade start up script for UN*X
##
##############################################################################

# Attempt to set APP_HOME
# Resolve links: $0 may be a link
PRG="$0"
# Need this for relative symlinks.
while [ -h "$PRG" ] ; do
    ls=`ls -ld "$PRG"`
    link=`expr "$ls" : '.*-> \(.*\)$'`
    if expr "$link" : '/.*' > /dev/null; then
        PRG="$link"
    else
        PRG=`dirname "$PRG"`"/$link"
    fi
done


APP_NAME="mkt-agent-sdk"
APP_BASE_NAME=`basename "$0"`

# Add default JVM options here. You can also use JAVA_OPTS and MKT_AGENT_UPGRADE_OPTS to pass JVM options to this script.
# MKT_AGENT_UPGRADE_OPTS=-agentlib:jdwp=transport=dt_socket,address=8003,server=y,suspend=n

warn ( ) {
    echo "$*"
}

die ( ) {
    echo
    echo "$*"
    echo
    exit 1
}


# Determine the Java command to use to start the JVM.
if [ -n "$JAVA_HOME" ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
        # IBM's JDK on AIX uses strange locations for the executables
        JAVACMD="$JAVA_HOME/jre/sh/java"
    else
        JAVACMD="$JAVA_HOME/bin/java"
    fi
    if [ ! -x "$JAVACMD" ] ; then
        die "ERROR: JAVA_HOME is set to an invalid directory: $JAVA_HOME

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
    fi
else
    JAVACMD="java"
    which java >/dev/null 2>&1 || die "ERROR: JAVA_HOME is not set and no 'java' command could be found in your PATH.

Please set the JAVA_HOME variable in your environment to match the
location of your Java installation."
fi


# Split up the JVM_OPTS And MKT_AGENT_UPGRADE_OPTS values into an array, following the shell quoting and substitution rules
function splitJvmOpts() {
    JVM_OPTS=("$@")
}

function download() {
    cd "`dirname \"$PRG\"`/.." >/dev/null
    APP_HOME="`pwd -P`"
    export APP_HOME
    CLASSPATH=$APP_HOME/config:$APP_HOME/lib/*
    DEFAULT_JVM_OPTS='"-Dlogback.configurationFile=$APP_HOME/config/logback-upgrade.xml"'
    eval splitJvmOpts $DEFAULT_JVM_OPTS $JAVA_OPTS $MKT_AGENT_UPGRADE_OPTS
    "$JAVACMD" "${JVM_OPTS[@]}" -classpath "$CLASSPATH" com.sas.mkt.apigw.sdk.streaming.agent.UpgradeAgent download
    if [ $? != "0" ] ; then
        die "Upgrade failed.   Check logs."
    fi
    if [ "$1." != "downloadonly." ] ; then
        exec $APP_HOME/new/mkt*/bin/mkt-agent-upgrade copyfiles
    fi
}

function copyfiles() {
    cd "`dirname \"$PRG\"`/.." >/dev/null
    DIRNAME="`pwd -P`"
    cd ../.. >/dev/null
    APP_HOME="`pwd -P`"
    export APP_HOME
    CLASSPATH=$DIRNAME/config:$DIRNAME/lib/*
    DEFAULT_JVM_OPTS='"-Dlogback.configurationFile=$APP_HOME/config/logback-upgrade.xml"'
    eval splitJvmOpts $DEFAULT_JVM_OPTS $JAVA_OPTS $MKT_AGENT_UPGRADE_OPTS
    "$JAVACMD" "${JVM_OPTS[@]}" -classpath "$CLASSPATH" com.sas.mkt.apigw.sdk.streaming.agent.UpgradeAgent copyfiles
    if [ $? != "0" ] ; then
        die "Upgrade failed.   Check logs."
    fi
    if [ "$1." != "copyfilesonly." ] ; then
        exec $APP_HOME/bin/mkt-agent-upgrade cleanup
    fi
}

function cleanup() {
    cd "`dirname \"$PRG\"`/.." >/dev/null
    APP_HOME="`pwd -P`"
    export APP_HOME
    CLASSPATH=$APP_HOME/config:$APP_HOME/lib/*
    DEFAULT_JVM_OPTS='"-Dlogback.configurationFile=$APP_HOME/config/logback-upgrade.xml"'
    eval splitJvmOpts $DEFAULT_JVM_OPTS $JAVA_OPTS $MKT_AGENT_UPGRADE_OPTS
    "$JAVACMD" "${JVM_OPTS[@]}" -classpath "$CLASSPATH" com.sas.mkt.apigw.sdk.streaming.agent.UpgradeAgent cleanup
    if [ "$?" != "0" ] ; then
        die "Upgrade failed.   Check logs."
    fi
}

if [ "$1." == "copyfiles." ] ; then 
     copyfiles $1
elif [ "$1." == "copyfilesonly." ] ; then 
     copyfiles $1
elif [ "$1." == "cleanup." ] ; then 
     cleanup $1
else
     download $1
fi

