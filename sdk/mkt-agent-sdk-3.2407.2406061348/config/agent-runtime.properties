// Task Executor properties
// Starting thread pool size
agent.runtime.taskexecutor.corePoolSize=50
// Thread pool can grow to this size as necessary
agent.runtime.taskexecutor.maxPoolSize=100
// Thread name prefix for logging
agent.runtime.taskExecutor.threadNamePrefix=ASMExecutor-
// Queue capacity. When all threads are occupied, tasks are queued. -1 denotes unbounded size.
agent.runtime.taskExecutor.queueCapacity=-1
// Wait 60 seconds for task executor shutdown (60 tries, waiting 1000 millis)
agent.runtime.taskExecutor.shutdown.retries=60
agent.runtime.taskExecutor.shutdown.waitTimeMillis=1000

// Task Scheduler properties
// Starting thread pool size
agent.runtime.taskScheduler.corePoolSize=10
// Wait 20 seconds to allow scheduled tasks to complete
agent.runtime.taskScheduler.shutdown.waitTimeMillis=20000

server.port=8080
